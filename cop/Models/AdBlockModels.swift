//
//  AdBlockModels.swift
//  cop
//
//  Created by Augment Agent on 2025/6/17.
//

import Foundation
import WebKit

// MARK: - 过滤列表类型
enum FilterListType: String, CaseIterable, Identifiable {
    case easyList = "easylist"
    case easyPrivacy = "easyprivacy"
    case fanboysAnnoyances = "fanboys_annoyances"
    case fanboysNotifications = "fanboys_notifications"
    case iDontCareAboutCookies = "i_dont_care_about_cookies"
    
    var id: String { rawValue }
    
    var displayName: String {
        switch self {
        case .easyList:
            return "EasyList (广告屏蔽)"
        case .easyPrivacy:
            return "EasyPrivacy (隐私保护)"
        case .fanboysAnnoyances:
            return "Fanboy's Annoyances (干扰元素)"
        case .fanboysNotifications:
            return "Fanboy's Notifications (通知屏蔽)"
        case .iDontCareAboutCookies:
            return "I Don't Care About Cookies"
        }
    }
    
    var description: String {
        switch self {
        case .easyList:
            return "屏蔽常见广告和跟踪器"
        case .easyPrivacy:
            return "增强隐私保护，屏蔽跟踪脚本"
        case .fanboysAnnoyances:
            return "屏蔽弹窗、社交媒体小部件等干扰元素"
        case .fanboysNotifications:
            return "屏蔽推送通知请求"
        case .iDontCareAboutCookies:
            return "自动处理Cookie同意弹窗"
        }
    }
    
    var downloadURL: String {
        switch self {
        case .easyList:
            return "https://easylist-downloads.adblockplus.org/easylist_nointernational.txt"
        case .easyPrivacy:
            return "https://easylist-downloads.adblockplus.org/easyprivacy_nointernational.txt"
        case .fanboysAnnoyances:
            return "https://easylist-downloads.adblockplus.org/fanboy-annoyance_nointernational.txt"
        case .fanboysNotifications:
            return "https://easylist-downloads.adblockplus.org/fanboy-notifications.txt"
        case .iDontCareAboutCookies:
            return "https://www.i-dont-care-about-cookies.eu/abp/"
        }
    }
    
    var priority: Int {
        switch self {
        case .easyList:
            return 1 // 最高优先级
        case .easyPrivacy:
            return 2
        case .fanboysAnnoyances:
            return 3
        case .fanboysNotifications:
            return 4
        case .iDontCareAboutCookies:
            return 5
        }
    }
}

// MARK: - 过滤列表状态
enum FilterListStatus {
    case notDownloaded
    case downloading
    case downloaded
    case compiling
    case compiled
    case applied
    case error(String)
    
    var displayText: String {
        switch self {
        case .notDownloaded:
            return "未下载"
        case .downloading:
            return "下载中..."
        case .downloaded:
            return "已下载"
        case .compiling:
            return "编译中..."
        case .compiled:
            return "已编译"
        case .applied:
            return "已应用"
        case .error(let message):
            return "错误: \(message)"
        }
    }
    
    var isActive: Bool {
        switch self {
        case .applied:
            return true
        default:
            return false
        }
    }
}

// MARK: - 过滤列表信息
struct FilterListInfo: Identifiable {
    let id = UUID()
    let type: FilterListType
    var status: FilterListStatus = .notDownloaded
    var lastUpdated: Date?
    var ruleCount: Int = 0
    var isEnabled: Bool = true
    var downloadSize: Int64 = 0
    var compiledSize: Int64 = 0
    
    var displaySize: String {
        if downloadSize > 0 {
            return ByteCountFormatter.string(fromByteCount: downloadSize, countStyle: .file)
        }
        return "未知"
    }
    
    var lastUpdatedText: String {
        guard let lastUpdated = lastUpdated else {
            return "从未更新"
        }
        
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: lastUpdated, relativeTo: Date())
    }
}

// MARK: - 广告屏蔽统计
struct AdBlockStatistics {
    var totalRulesApplied: Int = 0
    var totalRequestsBlocked: Int = 0
    var totalBytesBlocked: Int64 = 0
    var averagePageLoadImprovement: Double = 0.0
    var lastResetDate: Date = Date()
    
    var displayBytesBlocked: String {
        return ByteCountFormatter.string(fromByteCount: totalBytesBlocked, countStyle: .file)
    }
    
    var averagePageLoadImprovementText: String {
        if averagePageLoadImprovement > 0 {
            return String(format: "%.1f%%", averagePageLoadImprovement * 100)
        }
        return "计算中..."
    }
    
    mutating func recordBlockedRequest(size: Int64 = 0) {
        totalRequestsBlocked += 1
        totalBytesBlocked += size
    }
    
    mutating func reset() {
        totalRequestsBlocked = 0
        totalBytesBlocked = 0
        averagePageLoadImprovement = 0.0
        lastResetDate = Date()
    }
}

// MARK: - 广告屏蔽配置
struct AdBlockConfiguration {
    var isEnabled: Bool = true
    var enabledFilterLists: Set<FilterListType> = [.easyList, .easyPrivacy]
    var maxRulesPerList: Int = 150000 // iOS 18支持的上限
    var autoUpdateInterval: TimeInterval = 24 * 60 * 60 // 24小时
    var enableStatistics: Bool = true
    var trustedDomains: Set<String> = []
    
    // 性能优化设置
    var enableRuleOptimization: Bool = true
    var enableMemoryOptimization: Bool = true
    var enableBackgroundCompilation: Bool = false // 仅手动更新
    
    static let `default` = AdBlockConfiguration()
}

// MARK: - 内容规则列表信息
struct ContentRuleListInfo {
    let identifier: String
    let filterListType: FilterListType
    var ruleCount: Int = 0
    var compilationTime: TimeInterval = 0
    var memoryUsage: Int64 = 0
    var isActive: Bool = false
    
    var displayMemoryUsage: String {
        return ByteCountFormatter.string(fromByteCount: memoryUsage, countStyle: .memory)
    }
    
    var displayCompilationTime: String {
        return String(format: "%.2fs", compilationTime)
    }
}

// MARK: - 广告屏蔽错误类型
enum AdBlockError: LocalizedError {
    case downloadFailed(FilterListType, String)
    case parseFailed(FilterListType, String)
    case compilationFailed(FilterListType, String)
    case tooManyRules(FilterListType, Int, Int)
    case networkError(String)
    case storageError(String)
    
    var errorDescription: String? {
        switch self {
        case .downloadFailed(let type, let message):
            return "下载 \(type.displayName) 失败: \(message)"
        case .parseFailed(let type, let message):
            return "解析 \(type.displayName) 失败: \(message)"
        case .compilationFailed(let type, let message):
            return "编译 \(type.displayName) 失败: \(message)"
        case .tooManyRules(let type, let count, let limit):
            return "\(type.displayName) 规则数量过多: \(count)/\(limit)"
        case .networkError(let message):
            return "网络错误: \(message)"
        case .storageError(let message):
            return "存储错误: \(message)"
        }
    }
}

// MARK: - 广告屏蔽事件
enum AdBlockEvent {
    case downloadStarted(FilterListType)
    case downloadCompleted(FilterListType, Int64)
    case compilationStarted(FilterListType)
    case compilationCompleted(FilterListType, Int)
    case ruleListApplied(FilterListType)
    case ruleListRemoved(FilterListType)
    case requestBlocked(String, Int64)
    case error(AdBlockError)
    
    var displayMessage: String {
        switch self {
        case .downloadStarted(let type):
            return "开始下载 \(type.displayName)"
        case .downloadCompleted(let type, let size):
            let sizeText = ByteCountFormatter.string(fromByteCount: size, countStyle: .file)
            return "\(type.displayName) 下载完成 (\(sizeText))"
        case .compilationStarted(let type):
            return "开始编译 \(type.displayName)"
        case .compilationCompleted(let type, let ruleCount):
            return "\(type.displayName) 编译完成 (\(ruleCount) 条规则)"
        case .ruleListApplied(let type):
            return "\(type.displayName) 已应用"
        case .ruleListRemoved(let type):
            return "\(type.displayName) 已移除"
        case .requestBlocked(let url, let size):
            let sizeText = size > 0 ? " (\(ByteCountFormatter.string(fromByteCount: size, countStyle: .file)))" : ""
            return "已屏蔽: \(url)\(sizeText)"
        case .error(let error):
            return error.localizedDescription
        }
    }
}
