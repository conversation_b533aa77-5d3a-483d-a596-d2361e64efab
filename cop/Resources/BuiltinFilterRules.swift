//
//  BuiltinFilterRules.swift
//  cop
//
//  Created by Augment Agent on 2025/6/17.
//

import Foundation

/// 内置过滤规则 - 提供基础的广告屏蔽规则，无需网络下载
struct BuiltinFilterRules {
    
    /// 基础EasyList规则 - 屏蔽常见广告
    static let basicEasyList = """
! Basic EasyList Rules for iOS Browser
! Version: 1.0
! Last modified: 2025-06-17
! Homepage: https://github.com/cop/adblock

! *** 通用广告屏蔽规则 ***

! Google Ads
||googleadservices.com^
||googlesyndication.com^
||googletagservices.com^
||doubleclick.net^
||adsystem.google.com^

! Facebook/Meta Ads
||facebook.com/tr^
||connect.facebook.net^$script
||facebook.com/plugins/like.php^

! Amazon Ads
||amazon-adsystem.com^
||media-amazon.com^$script

! 常见广告网络
||adsystem.com^
||adnxs.com^
||adsafeprotected.com^
||moatads.com^
||scorecardresearch.com^
||outbrain.com^
||taboola.com^
||revcontent.com^

! 中国广告网络
||baidu.com/cpro^
||pos.baidu.com^
||cbjs.baidu.com^
||tanx.com^
||alimama.com^
||mmstat.com^

! 视频广告
||youtube.com/get_video_info^$xmlhttprequest
||youtube.com/ptracking^
||googlevideo.com/videoplayback^$script

! 社交媒体跟踪
||twitter.com/i/jot^
||analytics.twitter.com^
||linkedin.com/li/track^

! 分析和跟踪
||google-analytics.com^
||googletagmanager.com^
||hotjar.com^
||fullstory.com^
||mixpanel.com^
||segment.com^
||amplitude.com^

! CDN上的广告脚本
||ajax.googleapis.com/ajax/libs/jquery/*/jquery.min.js$script,domain=~jquery.com
/ads.js$script
/advertisement.js$script
/google_ads.js$script

! *** 元素隐藏规则 ***

! 通用广告容器
##.advertisement
##.ads
##.ad-container
##.ad-banner
##.ad-block
##.google-ads
##.adsense
##[class*="ad-"]
##[id*="ad-"]
##[class*="ads-"]
##[id*="ads-"]

! 弹窗和覆盖层
##.popup
##.modal-ad
##.overlay-ad
##.interstitial
##.lightbox-ad

! 社交媒体小部件
##.fb-like
##.twitter-follow
##.social-share
##.share-buttons

! 通知栏
##.notification-bar
##.cookie-notice
##.gdpr-notice
##.privacy-notice

! 视频广告
##.video-ads
##.preroll-ad
##.midroll-ad
##.postroll-ad

! 移动端特定
##.mobile-ad
##.banner-mobile
##.interstitial-mobile

! 中文网站常见
##.guanggao
##.ad_content
##.advertisement_content
##.ads_content

! *** 异常规则（白名单）***

! 允许必要的分析脚本
@@||google-analytics.com/analytics.js$script,domain=github.com
@@||googletagmanager.com/gtm.js$script,domain=stackoverflow.com

! 允许支付相关
@@||js.stripe.com^
@@||paypal.com^
@@||alipay.com^

! 允许CDN资源
@@||cdnjs.cloudflare.com^
@@||unpkg.com^
@@||jsdelivr.net^

! 允许开发工具
@@||github.com^
@@||stackoverflow.com^
@@||developer.mozilla.org^

! *** 特定网站规则 ***

! YouTube
youtube.com##.ytd-display-ad-renderer
youtube.com##.ytd-promoted-sparkles-web-renderer
youtube.com##.ytd-ad-slot-renderer
youtube.com##[class*="ytd-ad"]

! 百度
baidu.com##.result-op
baidu.com##.ec_wise_ad
baidu.com##[class*="ad"]

! 微博
weibo.com##.card-ad
weibo.com##.promote-card
weibo.com##[class*="ad_"]

! 知乎
zhihu.com##.AdblockBanner
zhihu.com##.Pc-word
zhihu.com##[class*="Ad-"]

! 淘宝/天猫
taobao.com##.J_TWidget
tmall.com##.tm-widget

! 京东
jd.com##.mod-ad
jd.com##[class*="ad-"]

! 新浪
sina.com.cn##.ad_content
sina.com.cn##[class*="ad_"]

! 搜狐
sohu.com##.ad-box
sohu.com##[class*="ad-"]

! 网易
163.com##.at_item
163.com##[class*="ad_"]

! *** 移动端优化规则 ***

! 移动端弹窗
##.mobile-popup
##.app-download-banner
##.install-app-banner
##.download-prompt

! 移动端广告位
##.mobile-banner
##.mobile-rectangle
##.mobile-leaderboard

! 底部固定广告
##.sticky-ad
##.fixed-bottom-ad
##.floating-ad

! *** 性能优化规则 ***

! 阻止大型广告资源
/ads/*$image,domain=~ads.com
/advertisements/*$image
/banners/*$image,domain=~banner.com
/popups/*$script

! 阻止跟踪像素
||*$image,domain=facebook.com|twitter.com|linkedin.com,third-party
||*/tracking.gif$image
||*/pixel.gif$image
||*/beacon.gif$image

! *** 隐私保护规则 ***

! 阻止指纹识别
/fingerprint.js$script
/canvas-fingerprint.js$script
/device-fingerprint.js$script

! 阻止用户行为跟踪
/mouseflow.js$script
/hotjar.js$script
/crazyegg.js$script
/clicktale.js$script

! 阻止A/B测试
/optimizely.js$script
/google-optimize.js$script
/vwo.js$script

! *** 恶意软件和钓鱼保护 ***

! 已知恶意域名
||malware-example.com^
||phishing-example.com^
||scam-example.com^

! 可疑的重定向
/redirect.php^$popup
/redir.php^$popup
/goto.php^$popup

! 加密货币挖矿
||coinhive.com^
||coin-hive.com^
||jsecoin.com^
||crypto-loot.com^
/coinhive.min.js$script
/cryptonight.js$script
"""
    
    /// 基础EasyPrivacy规则 - 隐私保护
    static let basicEasyPrivacy = """
! Basic EasyPrivacy Rules for iOS Browser
! Version: 1.0
! Last modified: 2025-06-17

! *** 跟踪和分析屏蔽 ***

! Google跟踪
||google-analytics.com^
||googletagmanager.com^
||googleadservices.com/pagead/conversion^
||google.com/pagead/1p-user-list^

! Facebook跟踪
||facebook.com/tr^
||connect.facebook.net/*/fbevents.js
||facebook.com/plugins/like.php^

! 其他主要跟踪器
||scorecardresearch.com^
||quantserve.com^
||omniture.com^
||adobe.com/b/ss/^
||chartbeat.com^
||newrelic.com^

! 中国跟踪器
||cnzz.com^
||baidu.com/hm.js^
||51.la^
||umeng.com^

! 社交媒体跟踪
||twitter.com/i/jot^
||linkedin.com/li/track^
||pinterest.com/ct/^

! 电商跟踪
||amazon.com/gp/aw/cr^
||alibaba.com/trace^
||taobao.com/go/rgn/^

! 移动应用跟踪
||app-measurement.com^
||firebase.com/v1/projects/^
||crashlytics.com^

! 邮件跟踪
||mailchimp.com/ping^
||constantcontact.com/images/^
||sendgrid.com/wf/open^

! 热图和会话录制
||hotjar.com^
||fullstory.com^
||logrocket.com^
||smartlook.com^

! A/B测试平台
||optimizely.com^
||google-optimize.com^
||vwo.com^
||unbounce.com^
"""
    
    /// Fanboy's Annoyances规则 - 屏蔽干扰元素
    static let basicFanboyAnnoyances = """
! Basic Fanboy's Annoyances Rules for iOS Browser
! Version: 1.0
! Last modified: 2025-06-17

! *** 社交媒体小部件 ***
##.fb-like
##.fb-share-button
##.twitter-follow-button
##.twitter-share-button
##.linkedin-share
##.pinterest-save
##.social-sharing
##.share-buttons
##.addthis_toolbox
##.sharethis-inline-share-buttons

! *** 弹窗和模态框 ***
##.popup
##.modal
##.lightbox
##.overlay
##.interstitial
##.newsletter-popup
##.email-signup
##.subscribe-popup
##.exit-intent

! *** 通知和提示 ***
##.notification-bar
##.alert-bar
##.cookie-notice
##.gdpr-notice
##.privacy-notice
##.age-verification
##.location-prompt
##.push-notification-prompt

! *** 应用推广 ***
##.app-banner
##.download-app
##.install-app
##.mobile-app-banner
##.app-store-badge
##.google-play-badge

! *** 评论和评分 ***
##.rating-widget
##.review-prompt
##.feedback-widget
##.survey-popup
##.nps-survey

! *** 聊天和客服 ***
##.chat-widget
##.live-chat
##.customer-service
##.help-widget
##.support-bubble

! *** 视频相关干扰 ***
##.video-overlay
##.video-popup
##.autoplay-notice
##.video-ads-overlay

! *** 移动端特定 ***
##.mobile-sticky-header
##.mobile-bottom-bar
##.mobile-floating-button
##.mobile-app-install

! *** 中文网站常见 ***
##.weixin-tip
##.wechat-share
##.qq-share
##.weibo-share
##.download-tip
##.mobile-tip
"""
    
    /// 获取指定类型的内置规则
    static func getRules(for type: FilterListType) -> String {
        switch type {
        case .easyList:
            return basicEasyList
        case .easyPrivacy:
            return basicEasyPrivacy
        case .fanboysAnnoyances:
            return basicFanboyAnnoyances
        case .fanboysNotifications:
            return """
            ! Basic Fanboy's Notifications Rules
            ##.push-notification
            ##.notification-request
            ##.allow-notifications
            ##.enable-notifications
            /push-notifications.js$script
            """
        case .iDontCareAboutCookies:
            return """
            ! Basic I Don't Care About Cookies Rules
            ##.cookie-banner
            ##.cookie-notice
            ##.cookie-consent
            ##.gdpr-banner
            ##.privacy-banner
            /cookie-consent.js$script
            """
        }
    }
    
    /// 获取所有内置规则的统计信息
    static func getStatistics() -> [FilterListType: Int] {
        var stats: [FilterListType: Int] = [:]
        
        for type in FilterListType.allCases {
            let rules = getRules(for: type)
            let lines = rules.components(separatedBy: .newlines)
            let ruleCount = lines.filter { line in
                let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)
                return !trimmed.isEmpty && !trimmed.hasPrefix("!")
            }.count
            stats[type] = ruleCount
        }
        
        return stats
    }
}
