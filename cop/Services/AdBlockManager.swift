//
//  AdBlockManager.swift
//  cop
//
//  Created by Augment Agent on 2025/6/17.
//

import Foundation
import WebKit
import Combine
import os.log

/// 广告屏蔽管理器 - 统一管理广告屏蔽功能的核心类
@MainActor
final class AdBlockManager: ObservableObject {
    
    // MARK: - 单例
    static let shared = AdBlockManager()
    
    // MARK: - 发布的状态
    @Published private(set) var isEnabled = true
    @Published private(set) var configuration = AdBlockConfiguration.default
    @Published private(set) var statistics = AdBlockStatistics()
    @Published private(set) var activeRuleLists: [FilterListType: WKContentRuleList] = [:]
    @Published private(set) var ruleListInfos: [FilterListType: ContentRuleListInfo] = [:]
    @Published private(set) var isProcessing = false
    @Published private(set) var lastError: AdBlockError?
    
    // MARK: - 子管理器
    let filterListManager = FilterListManager()
    let ruleCompiler = RuleCompiler()
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.adblock", category: "AdBlockManager")
    private let userDefaults = UserDefaults.standard
    private var cancellables = Set<AnyCancellable>()
    private var eventSubject = PassthroughSubject<AdBlockEvent, Never>()
    
    // MARK: - 常量
    private struct Constants {
        static let configurationKey = "AdBlockConfiguration"
        static let statisticsKey = "AdBlockStatistics"
        static let ruleListPrefix = "cop_adblock_"
    }
    
    private init() {
        loadConfiguration()
        loadStatistics()
        setupObservers()
        
        logger.info("✅ AdBlockManager 初始化完成")
    }
    
    // MARK: - 公共接口
    
    /// 事件流
    var events: AnyPublisher<AdBlockEvent, Never> {
        eventSubject.eraseToAnyPublisher()
    }
    
    /// 启用或禁用广告屏蔽
    func setEnabled(_ enabled: Bool) {
        guard isEnabled != enabled else { return }
        
        isEnabled = enabled
        configuration.isEnabled = enabled
        saveConfiguration()
        
        if enabled {
            logger.info("✅ 广告屏蔽已启用")
            Task {
                await applyActiveRuleLists()
            }
        } else {
            logger.info("❌ 广告屏蔽已禁用")
            Task {
                await removeAllRuleLists()
            }
        }
        
        eventSubject.send(.ruleListApplied(.easyList)) // 临时事件
    }
    
    /// 更新配置
    func updateConfiguration(_ newConfiguration: AdBlockConfiguration) {
        let oldEnabledLists = configuration.enabledFilterLists
        configuration = newConfiguration
        saveConfiguration()
        
        // 检查是否需要更新规则列表
        if oldEnabledLists != newConfiguration.enabledFilterLists {
            Task {
                await refreshRuleLists()
            }
        }
        
        logger.info("🔧 广告屏蔽配置已更新")
    }
    
    /// 手动更新所有过滤列表
    func updateAllFilterLists() async {
        guard !isProcessing else {
            logger.warning("⚠️ 更新已在进行中")
            return
        }
        
        isProcessing = true
        defer { isProcessing = false }
        
        logger.info("🔄 开始更新所有过滤列表")
        
        // 更新过滤列表
        await filterListManager.updateAllFilterLists()
        
        // 重新编译和应用规则
        await refreshRuleLists()
        
        logger.info("✅ 所有过滤列表更新完成")
    }
    
    /// 应用规则列表到WebView配置
    func applyToWebViewConfiguration(_ configuration: WKWebViewConfiguration) {
        guard isEnabled else { return }
        
        for (_, ruleList) in activeRuleLists {
            configuration.userContentController.add(ruleList)
        }
        
        logger.info("🔧 已将 \(activeRuleLists.count) 个规则列表应用到WebView配置")
    }
    
    /// 移除WebView配置中的规则列表
    func removeFromWebViewConfiguration(_ configuration: WKWebViewConfiguration) {
        for (_, ruleList) in activeRuleLists {
            configuration.userContentController.remove(ruleList)
        }
        
        logger.info("🧹 已从WebView配置中移除所有规则列表")
    }
    
    /// 记录被屏蔽的请求
    func recordBlockedRequest(url: String, estimatedSize: Int64 = 0) {
        guard configuration.enableStatistics else { return }
        
        statistics.recordBlockedRequest(size: estimatedSize)
        saveStatistics()
        
        eventSubject.send(.requestBlocked(url, estimatedSize))
    }
    
    /// 重置统计数据
    func resetStatistics() {
        statistics.reset()
        saveStatistics()
        logger.info("📊 统计数据已重置")
    }
    
    /// 获取规则列表状态摘要
    func getRuleListSummary() -> String {
        let totalRules = ruleListInfos.values.reduce(0) { $0 + $1.ruleCount }
        let activeCount = activeRuleLists.count
        let enabledCount = configuration.enabledFilterLists.count
        
        return "活跃: \(activeCount)/\(enabledCount), 总规则: \(totalRules)"
    }
    
    // MARK: - 私有方法
    
    private func setupObservers() {
        // 监听过滤列表管理器的状态变化
        filterListManager.$filterLists
            .sink { [weak self] _ in
                // 过滤列表状态变化时可能需要重新编译
            }
            .store(in: &cancellables)
        
        // 监听编译器状态
        ruleCompiler.$lastError
            .compactMap { $0 }
            .sink { [weak self] error in
                self?.lastError = error
                self?.eventSubject.send(.error(error))
            }
            .store(in: &cancellables)
    }
    
    private func refreshRuleLists() async {
        guard isEnabled else { return }
        
        logger.info("🔄 刷新规则列表")
        
        // 移除现有规则列表
        await removeAllRuleLists()
        
        // 编译并应用启用的过滤列表
        for filterType in configuration.enabledFilterLists {
            await compileAndApplyFilterList(filterType)
        }
        
        logger.info("✅ 规则列表刷新完成")
    }
    
    private func compileAndApplyFilterList(_ type: FilterListType) async {
        guard let content = filterListManager.getFilterListContent(for: type) else {
            logger.warning("⚠️ 无法获取 \(type.displayName) 的内容")
            return
        }
        
        do {
            eventSubject.send(.compilationStarted(type))
            
            // 编译规则
            let jsonRules = try await ruleCompiler.compileFilterList(content, type: type)
            
            // 创建WKContentRuleList
            let identifier = Constants.ruleListPrefix + type.rawValue
            let ruleList = try await compileContentRuleList(identifier: identifier, jsonRules: jsonRules)
            
            // 保存规则列表
            activeRuleLists[type] = ruleList
            
            // 更新信息
            let ruleCount = estimateRuleCount(from: jsonRules)
            ruleListInfos[type] = ContentRuleListInfo(
                identifier: identifier,
                filterListType: type,
                ruleCount: ruleCount,
                isActive: true
            )
            
            statistics.totalRulesApplied += ruleCount
            saveStatistics()
            
            eventSubject.send(.compilationCompleted(type, ruleCount))
            eventSubject.send(.ruleListApplied(type))
            
            logger.info("✅ \(type.displayName) 规则列表已应用 (\(ruleCount) 条规则)")
            
        } catch {
            let adBlockError = error as? AdBlockError ?? .compilationFailed(type, error.localizedDescription)
            lastError = adBlockError
            eventSubject.send(.error(adBlockError))
            
            logger.error("❌ 应用 \(type.displayName) 失败: \(adBlockError.localizedDescription)")
        }
    }
    
    private func compileContentRuleList(identifier: String, jsonRules: String) async throws -> WKContentRuleList {
        return try await withCheckedThrowingContinuation { continuation in
            WKContentRuleListStore.default().compileContentRuleList(
                forIdentifier: identifier,
                encodedContentRuleList: jsonRules
            ) { ruleList, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else if let ruleList = ruleList {
                    continuation.resume(returning: ruleList)
                } else {
                    continuation.resume(throwing: AdBlockError.compilationFailed(.easyList, "未知编译错误"))
                }
            }
        }
    }
    
    private func removeAllRuleLists() async {
        for (type, _) in activeRuleLists {
            let identifier = Constants.ruleListPrefix + type.rawValue
            
            do {
                try await removeContentRuleList(identifier: identifier)
                eventSubject.send(.ruleListRemoved(type))
            } catch {
                logger.error("❌ 移除规则列表失败: \(type.displayName) - \(error.localizedDescription)")
            }
        }
        
        activeRuleLists.removeAll()
        ruleListInfos.removeAll()
        
        logger.info("🧹 所有规则列表已移除")
    }
    
    private func removeContentRuleList(identifier: String) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            WKContentRuleListStore.default().removeContentRuleList(forIdentifier: identifier) { error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else {
                    continuation.resume()
                }
            }
        }
    }
    
    private func applyActiveRuleLists() async {
        // 这个方法将在BrowserManager中调用，用于将规则应用到新创建的WebView
        logger.info("🔧 准备应用活跃规则列表")
    }
    
    private func estimateRuleCount(from jsonRules: String) -> Int {
        // 简单估算JSON中的规则数量
        let triggerCount = jsonRules.components(separatedBy: "\"trigger\"").count - 1
        return max(triggerCount, 0)
    }
    
    // MARK: - 持久化
    
    private func saveConfiguration() {
        do {
            let data = try JSONEncoder().encode(configuration)
            userDefaults.set(data, forKey: Constants.configurationKey)
        } catch {
            logger.error("❌ 保存配置失败: \(error.localizedDescription)")
        }
    }
    
    private func loadConfiguration() {
        guard let data = userDefaults.data(forKey: Constants.configurationKey) else {
            configuration = .default
            return
        }
        
        do {
            configuration = try JSONDecoder().decode(AdBlockConfiguration.self, from: data)
            isEnabled = configuration.isEnabled
        } catch {
            logger.error("❌ 加载配置失败: \(error.localizedDescription)")
            configuration = .default
        }
    }
    
    private func saveStatistics() {
        do {
            let data = try JSONEncoder().encode(statistics)
            userDefaults.set(data, forKey: Constants.statisticsKey)
        } catch {
            logger.error("❌ 保存统计数据失败: \(error.localizedDescription)")
        }
    }
    
    private func loadStatistics() {
        guard let data = userDefaults.data(forKey: Constants.statisticsKey) else {
            return
        }
        
        do {
            statistics = try JSONDecoder().decode(AdBlockStatistics.self, from: data)
        } catch {
            logger.error("❌ 加载统计数据失败: \(error.localizedDescription)")
        }
    }
}

// MARK: - Codable 支持
extension AdBlockConfiguration: Codable {}
extension AdBlockStatistics: Codable {}
