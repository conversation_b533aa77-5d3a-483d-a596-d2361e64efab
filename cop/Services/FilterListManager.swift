//
//  FilterListManager.swift
//  cop
//
//  Created by Augment Agent on 2025/6/17.
//

import Foundation
import Combine
import os.log

/// 过滤列表管理器 - 负责下载、缓存和管理EasyList等过滤列表
@MainActor
final class FilterListManager: ObservableObject {
    
    // MARK: - 发布的状态
    @Published private(set) var filterLists: [FilterListType: FilterListInfo] = [:]
    @Published private(set) var isUpdating = false
    @Published private(set) var lastError: AdBlockError?
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.adblock", category: "FilterListManager")
    private let urlSession: URLSession
    private let cacheDirectory: URL
    private let userDefaults = UserDefaults.standard
    private var updateTasks: [FilterListType: Task<Void, Never>] = [:]
    
    // MARK: - 常量
    private struct Constants {
        static let cacheDirectoryName = "FilterLists"
        static let maxRetryAttempts = 3
        static let downloadTimeout: TimeInterval = 60.0
        static let userDefaultsKey = "FilterListsInfo"
    }
    
    init() {
        // 配置URLSession
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = Constants.downloadTimeout
        config.timeoutIntervalForResource = Constants.downloadTimeout * 2
        config.requestCachePolicy = .reloadIgnoringLocalCacheData
        self.urlSession = URLSession(configuration: config)
        
        // 设置缓存目录
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        self.cacheDirectory = documentsPath.appendingPathComponent(Constants.cacheDirectoryName)
        
        // 创建缓存目录
        createCacheDirectoryIfNeeded()
        
        // 初始化过滤列表信息
        initializeFilterLists()
        
        // 加载保存的状态
        loadSavedState()
        
        logger.info("✅ FilterListManager 初始化完成")
    }
    
    // MARK: - 公共接口
    
    /// 更新指定的过滤列表
    func updateFilterList(_ type: FilterListType) async {
        guard !isUpdating else {
            logger.warning("⚠️ 更新已在进行中，跳过 \(type.displayName)")
            return
        }
        
        // 取消现有的更新任务
        updateTasks[type]?.cancel()
        
        // 创建新的更新任务
        updateTasks[type] = Task {
            await performUpdate(for: type)
        }
        
        await updateTasks[type]?.value
    }
    
    /// 更新所有启用的过滤列表
    func updateAllFilterLists() async {
        guard !isUpdating else {
            logger.warning("⚠️ 批量更新已在进行中")
            return
        }
        
        isUpdating = true
        logger.info("🔄 开始批量更新过滤列表")
        
        defer {
            isUpdating = false
            saveState()
        }
        
        // 按优先级排序更新
        let sortedTypes = FilterListType.allCases.sorted { $0.priority < $1.priority }
        
        for type in sortedTypes {
            guard filterLists[type]?.isEnabled == true else { continue }
            
            await performUpdate(for: type)
            
            // 检查是否被取消
            if Task.isCancelled {
                logger.info("❌ 批量更新被取消")
                break
            }
        }
        
        logger.info("✅ 批量更新完成")
    }
    
    /// 获取过滤列表的本地文件路径
    func getLocalFilePath(for type: FilterListType) -> URL {
        return cacheDirectory.appendingPathComponent("\(type.rawValue).txt")
    }
    
    /// 获取过滤列表的原始内容
    func getFilterListContent(for type: FilterListType) -> String? {
        let filePath = getLocalFilePath(for: type)
        
        guard FileManager.default.fileExists(atPath: filePath.path) else {
            return nil
        }
        
        do {
            return try String(contentsOf: filePath, encoding: .utf8)
        } catch {
            logger.error("❌ 读取过滤列表失败: \(type.displayName) - \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 启用或禁用过滤列表
    func setFilterListEnabled(_ type: FilterListType, enabled: Bool) {
        filterLists[type]?.isEnabled = enabled
        saveState()
        logger.info("🔧 \(type.displayName) \(enabled ? "已启用" : "已禁用")")
    }
    
    /// 清除所有缓存的过滤列表
    func clearAllCaches() {
        logger.info("🧹 清除所有过滤列表缓存")
        
        do {
            let files = try FileManager.default.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            for file in files {
                try FileManager.default.removeItem(at: file)
            }
            
            // 重置状态
            for type in FilterListType.allCases {
                filterLists[type]?.status = .notDownloaded
                filterLists[type]?.lastUpdated = nil
                filterLists[type]?.ruleCount = 0
                filterLists[type]?.downloadSize = 0
            }
            
            saveState()
            logger.info("✅ 缓存清除完成")
            
        } catch {
            logger.error("❌ 清除缓存失败: \(error.localizedDescription)")
            lastError = .storageError(error.localizedDescription)
        }
    }
    
    // MARK: - 私有方法
    
    private func createCacheDirectoryIfNeeded() {
        do {
            try FileManager.default.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
        } catch {
            logger.error("❌ 创建缓存目录失败: \(error.localizedDescription)")
        }
    }
    
    private func initializeFilterLists() {
        for type in FilterListType.allCases {
            filterLists[type] = FilterListInfo(type: type)
        }
    }
    
    private func performUpdate(for type: FilterListType) async {
        logger.info("🔄 开始更新 \(type.displayName)")
        
        // 更新状态
        filterLists[type]?.status = .downloading
        
        do {
            // 下载过滤列表
            let (data, response) = try await downloadFilterList(type)
            
            // 验证响应
            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                throw AdBlockError.downloadFailed(type, "HTTP状态码错误")
            }
            
            // 保存到本地
            let filePath = getLocalFilePath(for: type)
            try data.write(to: filePath)
            
            // 更新信息
            filterLists[type]?.status = .downloaded
            filterLists[type]?.lastUpdated = Date()
            filterLists[type]?.downloadSize = Int64(data.count)
            
            // 计算规则数量（简单估算）
            if let content = String(data: data, encoding: .utf8) {
                let ruleCount = estimateRuleCount(from: content)
                filterLists[type]?.ruleCount = ruleCount
            }
            
            logger.info("✅ \(type.displayName) 更新完成")
            
        } catch {
            logger.error("❌ 更新 \(type.displayName) 失败: \(error.localizedDescription)")
            
            let adBlockError: AdBlockError
            if error is AdBlockError {
                adBlockError = error as! AdBlockError
            } else {
                adBlockError = .downloadFailed(type, error.localizedDescription)
            }
            
            filterLists[type]?.status = .error(adBlockError.localizedDescription)
            lastError = adBlockError
        }
    }
    
    private func downloadFilterList(_ type: FilterListType) async throws -> (Data, URLResponse) {
        guard let url = URL(string: type.downloadURL) else {
            throw AdBlockError.downloadFailed(type, "无效的下载URL")
        }
        
        var request = URLRequest(url: url)
        request.setValue("Mozilla/5.0 (iPad; CPU OS 18_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/604.1", forHTTPHeaderField: "User-Agent")
        
        return try await urlSession.data(for: request)
    }
    
    private func estimateRuleCount(from content: String) -> Int {
        let lines = content.components(separatedBy: .newlines)
        var ruleCount = 0
        
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 跳过注释和空行
            if trimmed.isEmpty || trimmed.hasPrefix("!") || trimmed.hasPrefix("[") {
                continue
            }
            
            ruleCount += 1
        }
        
        return ruleCount
    }
    
    private func saveState() {
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        
        do {
            let data = try encoder.encode(filterLists)
            userDefaults.set(data, forKey: Constants.userDefaultsKey)
        } catch {
            logger.error("❌ 保存状态失败: \(error.localizedDescription)")
        }
    }
    
    private func loadSavedState() {
        guard let data = userDefaults.data(forKey: Constants.userDefaultsKey) else {
            return
        }
        
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        do {
            let savedFilterLists = try decoder.decode([FilterListType: FilterListInfo].self, from: data)
            
            // 合并保存的状态
            for (type, info) in savedFilterLists {
                if var currentInfo = filterLists[type] {
                    currentInfo.isEnabled = info.isEnabled
                    currentInfo.lastUpdated = info.lastUpdated
                    currentInfo.ruleCount = info.ruleCount
                    currentInfo.downloadSize = info.downloadSize
                    
                    // 检查文件是否仍然存在
                    let filePath = getLocalFilePath(for: type)
                    if FileManager.default.fileExists(atPath: filePath.path) {
                        currentInfo.status = .downloaded
                    } else {
                        currentInfo.status = .notDownloaded
                    }
                    
                    filterLists[type] = currentInfo
                }
            }
            
            logger.info("✅ 状态加载完成")
            
        } catch {
            logger.error("❌ 加载状态失败: \(error.localizedDescription)")
        }
    }
}

// MARK: - FilterListInfo Codable 支持
extension FilterListInfo: Codable {
    enum CodingKeys: String, CodingKey {
        case type, isEnabled, lastUpdated, ruleCount, downloadSize
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.type = try container.decode(FilterListType.self, from: .type)
        self.isEnabled = try container.decode(Bool.self, forKey: .isEnabled)
        self.lastUpdated = try container.decodeIfPresent(Date.self, forKey: .lastUpdated)
        self.ruleCount = try container.decode(Int.self, forKey: .ruleCount)
        self.downloadSize = try container.decode(Int64.self, forKey: .downloadSize)
        self.status = .notDownloaded // 状态不保存，启动时重新检查
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(type, forKey: .type)
        try container.encode(isEnabled, forKey: .isEnabled)
        try container.encodeIfPresent(lastUpdated, forKey: .lastUpdated)
        try container.encode(ruleCount, forKey: .ruleCount)
        try container.encode(downloadSize, forKey: .downloadSize)
    }
}
