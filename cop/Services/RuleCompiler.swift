//
//  RuleCompiler.swift
//  cop
//
//  Created by Augment Agent on 2025/6/17.
//

import Foundation
import WebKit
import os.log

/// 规则编译器 - 将EasyList格式的过滤规则转换为WKContentRuleList JSON格式
@MainActor
final class RuleCompiler: ObservableObject {
    
    // MARK: - 发布的状态
    @Published private(set) var isCompiling = false
    @Published private(set) var compilationProgress: Double = 0.0
    @Published private(set) var lastError: AdBlockError?
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.adblock", category: "RuleCompiler")
    private let maxRulesPerList: Int
    private let trustedDomains: Set<String>
    
    // MARK: - 编译统计
    struct CompilationStats {
        var totalRulesProcessed = 0
        var networkRules = 0
        var elementHidingRules = 0
        var exceptionRules = 0
        var skippedRules = 0
        var compilationTime: TimeInterval = 0
    }
    
    init(maxRulesPerList: Int = 150000, trustedDomains: Set<String> = []) {
        self.maxRulesPerList = maxRulesPerList
        self.trustedDomains = trustedDomains
        logger.info("✅ RuleCompiler 初始化完成 (最大规则数: \(maxRulesPerList))")
    }
    
    // MARK: - 公共接口
    
    /// 编译过滤列表为WKContentRuleList JSON
    func compileFilterList(_ content: String, type: FilterListType) async throws -> String {
        logger.info("🔄 开始编译 \(type.displayName)")
        
        isCompiling = true
        compilationProgress = 0.0
        lastError = nil
        
        defer {
            isCompiling = false
            compilationProgress = 0.0
        }
        
        let startTime = Date()
        var stats = CompilationStats()
        
        do {
            // 解析规则
            let rules = try await parseEasyListRules(content, type: type, stats: &stats)
            
            // 转换为JSON
            let jsonRules = try convertToWKContentRuleListJSON(rules, stats: &stats)
            
            stats.compilationTime = Date().timeIntervalSince(startTime)
            
            logger.info("✅ \(type.displayName) 编译完成")
            logger.info("📊 统计: 总规则 \(stats.totalRulesProcessed), 网络规则 \(stats.networkRules), 元素隐藏 \(stats.elementHidingRules), 异常规则 \(stats.exceptionRules), 跳过 \(stats.skippedRules)")
            logger.info("⏱️ 编译耗时: \(String(format: "%.2f", stats.compilationTime))秒")
            
            return jsonRules
            
        } catch {
            let adBlockError: AdBlockError
            if let abError = error as? AdBlockError {
                adBlockError = abError
            } else {
                adBlockError = .compilationFailed(type, error.localizedDescription)
            }
            
            lastError = adBlockError
            logger.error("❌ 编译 \(type.displayName) 失败: \(adBlockError.localizedDescription)")
            throw adBlockError
        }
    }
    
    // MARK: - 私有方法 - EasyList解析
    
    private func parseEasyListRules(_ content: String, type: FilterListType, stats: inout CompilationStats) async throws -> [WKContentRule] {
        let lines = content.components(separatedBy: .newlines)
        var rules: [WKContentRule] = []
        
        let totalLines = lines.count
        var processedLines = 0
        
        for line in lines {
            processedLines += 1
            
            // 更新进度
            if processedLines % 100 == 0 {
                compilationProgress = Double(processedLines) / Double(totalLines) * 0.8 // 80%用于解析
                
                // 让出控制权，避免阻塞UI
                await Task.yield()
            }
            
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 跳过注释、空行和元数据
            if trimmedLine.isEmpty || trimmedLine.hasPrefix("!") || trimmedLine.hasPrefix("[") {
                continue
            }
            
            stats.totalRulesProcessed += 1
            
            // 检查规则数量限制
            if rules.count >= maxRulesPerList {
                logger.warning("⚠️ 达到最大规则数限制: \(maxRulesPerList)")
                break
            }
            
            // 解析不同类型的规则
            if let rule = try parseEasyListRule(trimmedLine, stats: &stats) {
                rules.append(rule)
            } else {
                stats.skippedRules += 1
            }
        }
        
        return rules
    }
    
    private func parseEasyListRule(_ line: String, stats: inout CompilationStats) throws -> WKContentRule? {
        // 异常规则 (@@)
        if line.hasPrefix("@@") {
            stats.exceptionRules += 1
            return parseExceptionRule(String(line.dropFirst(2)))
        }
        
        // 元素隐藏规则 (##)
        if line.contains("##") {
            stats.elementHidingRules += 1
            return parseElementHidingRule(line)
        }
        
        // 元素隐藏异常规则 (#@#)
        if line.contains("#@#") {
            stats.exceptionRules += 1
            return parseElementHidingExceptionRule(line)
        }
        
        // 网络过滤规则
        stats.networkRules += 1
        return parseNetworkRule(line)
    }
    
    private func parseNetworkRule(_ rule: String) -> WKContentRule? {
        // 简化的网络规则解析
        var urlFilter = rule
        var resourceTypes: [String] = []
        var domains: [String] = []
        var excludeDomains: [String] = []
        var loadType: [String] = []
        
        // 处理选项 (如 $script,domain=example.com)
        if let dollarIndex = rule.lastIndex(of: "$") {
            let optionsString = String(rule[rule.index(after: dollarIndex)...])
            urlFilter = String(rule[..<dollarIndex])
            
            let options = optionsString.components(separatedBy: ",")
            for option in options {
                let trimmedOption = option.trimmingCharacters(in: .whitespacesAndNewlines)
                
                if trimmedOption.hasPrefix("domain=") {
                    let domainList = String(trimmedOption.dropFirst(7))
                    let domainComponents = domainList.components(separatedBy: "|")
                    
                    for domain in domainComponents {
                        if domain.hasPrefix("~") {
                            excludeDomains.append(String(domain.dropFirst()))
                        } else {
                            domains.append(domain)
                        }
                    }
                } else if trimmedOption == "third-party" {
                    loadType.append("third-party")
                } else if trimmedOption == "first-party" {
                    loadType.append("first-party")
                } else {
                    // 资源类型
                    switch trimmedOption {
                    case "script":
                        resourceTypes.append("script")
                    case "image":
                        resourceTypes.append("image")
                    case "stylesheet":
                        resourceTypes.append("style-sheet")
                    case "xmlhttprequest":
                        resourceTypes.append("xmlhttprequest")
                    case "media":
                        resourceTypes.append("media")
                    case "font":
                        resourceTypes.append("font")
                    case "document":
                        resourceTypes.append("document")
                    default:
                        break
                    }
                }
            }
        }
        
        // 转换EasyList URL模式为正则表达式
        let regexPattern = convertEasyListPatternToRegex(urlFilter)
        
        // 构建trigger
        var trigger: [String: Any] = ["url-filter": regexPattern]
        
        if !resourceTypes.isEmpty {
            trigger["resource-type"] = resourceTypes
        }
        
        if !domains.isEmpty {
            trigger["if-domain"] = domains
        }
        
        if !excludeDomains.isEmpty {
            trigger["unless-domain"] = excludeDomains
        }
        
        if !loadType.isEmpty {
            trigger["load-type"] = loadType
        }
        
        // 构建action
        let action: [String: Any] = ["type": "block"]
        
        return WKContentRule(trigger: trigger, action: action)
    }
    
    private func parseElementHidingRule(_ rule: String) -> WKContentRule? {
        guard let separatorRange = rule.range(of: "##") else { return nil }
        
        let domainPart = String(rule[..<separatorRange.lowerBound])
        let selector = String(rule[separatorRange.upperBound...])
        
        // 验证CSS选择器
        guard isValidCSSSelector(selector) else { return nil }
        
        var trigger: [String: Any] = ["url-filter": ".*"]
        
        // 处理域名限制
        if !domainPart.isEmpty {
            let domains = domainPart.components(separatedBy: ",").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            var ifDomains: [String] = []
            var unlessDomains: [String] = []
            
            for domain in domains {
                if domain.hasPrefix("~") {
                    unlessDomains.append(String(domain.dropFirst()))
                } else {
                    ifDomains.append(domain)
                }
            }
            
            if !ifDomains.isEmpty {
                trigger["if-domain"] = ifDomains
            }
            
            if !unlessDomains.isEmpty {
                trigger["unless-domain"] = unlessDomains
            }
        }
        
        let action: [String: Any] = [
            "type": "css-display-none",
            "selector": selector
        ]
        
        return WKContentRule(trigger: trigger, action: action)
    }
    
    private func parseExceptionRule(_ rule: String) -> WKContentRule? {
        // 简化的异常规则处理
        let regexPattern = convertEasyListPatternToRegex(rule)
        
        let trigger: [String: Any] = ["url-filter": regexPattern]
        let action: [String: Any] = ["type": "ignore-previous-rules"]
        
        return WKContentRule(trigger: trigger, action: action)
    }
    
    private func parseElementHidingExceptionRule(_ rule: String) -> WKContentRule? {
        // 元素隐藏异常规则处理
        guard let separatorRange = rule.range(of: "#@#") else { return nil }
        
        let selector = String(rule[separatorRange.upperBound...])
        
        let trigger: [String: Any] = ["url-filter": ".*"]
        let action: [String: Any] = ["type": "ignore-previous-rules"]
        
        return WKContentRule(trigger: trigger, action: action)
    }
    
    private func convertEasyListPatternToRegex(_ pattern: String) -> String {
        var regex = pattern

        // 首先转义正则表达式特殊字符（除了我们要处理的EasyList特殊字符）
        let specialChars = [".", "+", "?", "(", ")", "[", "]", "{", "}", "\\"]
        for char in specialChars {
            regex = regex.replacingOccurrences(of: char, with: "\\" + char)
        }

        // EasyList特殊字符转换
        // || 表示域名开始
        regex = regex.replacingOccurrences(of: "\\|\\|", with: "^https?://([^/]+\\.)?")

        // | 表示地址开始或结束
        regex = regex.replacingOccurrences(of: "\\|", with: "^")

        // ^ 表示分隔符（非字母数字字符）
        regex = regex.replacingOccurrences(of: "\\^", with: "[^a-zA-Z0-9.%-]")

        // * 表示通配符
        regex = regex.replacingOccurrences(of: "\\*", with: ".*")

        // 确保regex不为空
        if regex.isEmpty {
            regex = ".*"
        }

        return regex
    }
    
    private func isValidCSSSelector(_ selector: String) -> Bool {
        // 简单的CSS选择器验证
        let trimmed = selector.trimmingCharacters(in: .whitespacesAndNewlines)
        return !trimmed.isEmpty && !trimmed.contains("javascript:") && !trimmed.contains("<script")
    }
    
    private func convertToWKContentRuleListJSON(_ rules: [WKContentRule], stats: inout CompilationStats) throws -> String {
        compilationProgress = 0.9 // 90%
        
        let jsonRules = rules.map { rule in
            return [
                "trigger": rule.trigger,
                "action": rule.action
            ]
        }
        
        compilationProgress = 1.0 // 100%
        
        let jsonData = try JSONSerialization.data(withJSONObject: jsonRules, options: [.prettyPrinted])
        return String(data: jsonData, encoding: .utf8) ?? ""
    }
}

// MARK: - 辅助结构
private struct WKContentRule {
    let trigger: [String: Any]
    let action: [String: Any]
}
