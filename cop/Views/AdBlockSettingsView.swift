//
//  AdBlockSettingsView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/17.
//

import SwiftUI

struct AdBlockSettingsView: View {
    @StateObject private var adBlockManager = AdBlockManager.shared
    @State private var showingUpdateAlert = false
    @State private var showingResetAlert = false
    @State private var isUpdating = false
    
    var body: some View {
        List {
            // 主开关
            mainToggleSection
            
            // 过滤列表管理
            if adBlockManager.isEnabled {
                filterListsSection
                
                // 统计信息
                statisticsSection
                
                // 高级设置
                advancedSection
                
                // 操作按钮
                actionsSection
            }
        }
        .listStyle(InsetGroupedListStyle())
        .navigationTitle("广告屏蔽")
        .navigationBarTitleDisplayMode(.large)
        .alert("更新过滤列表", isPresented: $showingUpdateAlert) {
            Button("取消", role: .cancel) { }
            Button("更新") {
                Task {
                    isUpdating = true
                    await adBlockManager.updateAllFilterLists()
                    isUpdating = false
                }
            }
        } message: {
            Text("这将下载最新的广告屏蔽规则。更新可能需要几分钟时间。")
        }
        .alert("重置统计", isPresented: $showingResetAlert) {
            Button("取消", role: .cancel) { }
            Button("重置", role: .destructive) {
                adBlockManager.resetStatistics()
            }
        } message: {
            Text("这将清除所有广告屏蔽统计数据。")
        }
    }
    
    // MARK: - 主开关部分
    private var mainToggleSection: some View {
        Section {
            HStack {
                Image(systemName: "shield.fill")
                    .foregroundColor(adBlockManager.isEnabled ? .green : .gray)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("广告屏蔽")
                        .font(.headline)
                    
                    Text(adBlockManager.isEnabled ? "已启用 - 正在屏蔽广告和跟踪器" : "已禁用 - 允许所有内容加载")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: Binding(
                    get: { adBlockManager.isEnabled },
                    set: { adBlockManager.setEnabled($0) }
                ))
            }
            .padding(.vertical, 4)
        } header: {
            Text("状态")
        } footer: {
            if adBlockManager.isEnabled {
                Text("广告屏蔽使用WKContentRuleList技术，在网络层面阻止广告和跟踪器，提供更好的性能和隐私保护。")
            } else {
                Text("禁用广告屏蔽后，所有网站内容将正常加载，包括广告和跟踪器。")
            }
        }
    }
    
    // MARK: - 过滤列表部分
    private var filterListsSection: some View {
        Section {
            ForEach(FilterListType.allCases) { filterType in
                FilterListRow(
                    filterType: filterType,
                    filterListManager: adBlockManager.filterListManager,
                    isEnabled: adBlockManager.configuration.enabledFilterLists.contains(filterType)
                ) { enabled in
                    var newConfig = adBlockManager.configuration
                    if enabled {
                        newConfig.enabledFilterLists.insert(filterType)
                    } else {
                        newConfig.enabledFilterLists.remove(filterType)
                    }
                    adBlockManager.updateConfiguration(newConfig)
                }
            }
        } header: {
            Text("过滤列表")
        } footer: {
            Text("选择要启用的过滤列表。EasyList屏蔽常见广告，EasyPrivacy增强隐私保护。")
        }
    }
    
    // MARK: - 统计信息部分
    private var statisticsSection: some View {
        Section {
            StatisticRow(
                icon: "shield.checkered",
                title: "已屏蔽请求",
                value: "\(adBlockManager.statistics.totalRequestsBlocked)",
                color: .green
            )
            
            StatisticRow(
                icon: "arrow.down.circle",
                title: "节省流量",
                value: adBlockManager.statistics.displayBytesBlocked,
                color: .blue
            )
            
            StatisticRow(
                icon: "speedometer",
                title: "页面加载提升",
                value: adBlockManager.statistics.averagePageLoadImprovementText,
                color: .orange
            )
            
            StatisticRow(
                icon: "list.bullet",
                title: "活跃规则",
                value: adBlockManager.getRuleListSummary(),
                color: .purple
            )
        } header: {
            Text("统计信息")
        } footer: {
            Text("统计数据自 \(adBlockManager.statistics.lastResetDate.formatted(date: .abbreviated, time: .omitted)) 开始计算")
        }
    }
    
    // MARK: - 高级设置部分
    private var advancedSection: some View {
        Section {
            HStack {
                Image(systemName: "chart.bar")
                    .foregroundColor(.blue)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("启用统计")
                        .font(.body)
                    Text("记录屏蔽效果和性能数据")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: Binding(
                    get: { adBlockManager.configuration.enableStatistics },
                    set: { enabled in
                        var newConfig = adBlockManager.configuration
                        newConfig.enableStatistics = enabled
                        adBlockManager.updateConfiguration(newConfig)
                    }
                ))
            }
            .padding(.vertical, 2)
            
            HStack {
                Image(systemName: "gear")
                    .foregroundColor(.gray)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("规则优化")
                        .font(.body)
                    Text("自动优化规则以提高性能")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: Binding(
                    get: { adBlockManager.configuration.enableRuleOptimization },
                    set: { enabled in
                        var newConfig = adBlockManager.configuration
                        newConfig.enableRuleOptimization = enabled
                        adBlockManager.updateConfiguration(newConfig)
                    }
                ))
            }
            .padding(.vertical, 2)
        } header: {
            Text("高级设置")
        }
    }
    
    // MARK: - 操作按钮部分
    private var actionsSection: some View {
        Section {
            Button(action: {
                showingUpdateAlert = true
            }) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                        .foregroundColor(.blue)
                    
                    Text("更新过滤列表")
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    if isUpdating || adBlockManager.isProcessing {
                        ProgressView()
                            .scaleEffect(0.8)
                    }
                }
            }
            .disabled(isUpdating || adBlockManager.isProcessing)
            
            Button(action: {
                showingResetAlert = true
            }) {
                HStack {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                    
                    Text("重置统计数据")
                        .foregroundColor(.red)
                }
            }
        } header: {
            Text("操作")
        } footer: {
            Text("手动更新将下载最新的过滤规则。建议定期更新以获得最佳屏蔽效果。")
        }
    }
}

// MARK: - 过滤列表行组件
struct FilterListRow: View {
    let filterType: FilterListType
    let filterListManager: FilterListManager
    let isEnabled: Bool
    let onToggle: (Bool) -> Void
    
    private var filterInfo: FilterListInfo? {
        filterListManager.filterLists[filterType]
    }
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(filterType.displayName)
                    .font(.body)
                
                Text(filterType.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if let info = filterInfo {
                    HStack(spacing: 12) {
                        Label(info.status.displayText, systemImage: statusIcon)
                            .font(.caption2)
                            .foregroundColor(statusColor)
                        
                        if info.ruleCount > 0 {
                            Label("\(info.ruleCount) 规则", systemImage: "list.bullet")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        
                        if !info.lastUpdatedText.isEmpty {
                            Label(info.lastUpdatedText, systemImage: "clock")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            
            Spacer()
            
            Toggle("", isOn: Binding(
                get: { isEnabled },
                set: onToggle
            ))
        }
        .padding(.vertical, 4)
    }
    
    private var statusIcon: String {
        guard let info = filterInfo else { return "questionmark.circle" }
        
        switch info.status {
        case .notDownloaded:
            return "arrow.down.circle"
        case .downloading:
            return "arrow.down.circle.fill"
        case .downloaded, .compiled, .applied:
            return "checkmark.circle.fill"
        case .compiling:
            return "gear"
        case .error:
            return "exclamationmark.triangle.fill"
        }
    }
    
    private var statusColor: Color {
        guard let info = filterInfo else { return .gray }
        
        switch info.status {
        case .notDownloaded:
            return .gray
        case .downloading, .compiling:
            return .blue
        case .downloaded, .compiled, .applied:
            return .green
        case .error:
            return .red
        }
    }
}

// MARK: - 统计行组件
struct StatisticRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title3)
                .frame(width: 24)
            
            Text(title)
                .font(.body)
            
            Spacer()
            
            Text(value)
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
        .padding(.vertical, 2)
    }
}

#Preview {
    NavigationView {
        AdBlockSettingsView()
    }
}
