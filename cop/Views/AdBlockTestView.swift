//
//  AdBlockTestView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/17.
//

import SwiftUI
import WebKit

/// 广告屏蔽测试视图 - 用于验证广告屏蔽功能的效果
struct AdBlockTestView: View {
    @StateObject private var adBlockManager = AdBlockManager.shared
    @State private var selectedTestSite = AdBlockTestSite.adblockTester
    @State private var webView: WKWebView?
    @State private var isLoading = false
    @State private var loadError: String?
    @State private var showingResults = false
    
    var body: some View {
        VStack(spacing: 0) {
            // 控制面板
            controlPanel
            
            Divider()
            
            // WebView容器
            webViewContainer
            
            // 结果面板
            if showingResults {
                Divider()
                resultsPanel
            }
        }
        .navigationTitle("广告屏蔽测试")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                But<PERSON>("测试") {
                    runAdBlockTest()
                }
                .disabled(isLoading)
            }
        }
        .onAppear {
            setupWebView()
        }
    }
    
    // MARK: - 控制面板
    private var controlPanel: some View {
        VStack(spacing: 16) {
            // 广告屏蔽状态
            HStack {
                Image(systemName: adBlockManager.isEnabled ? "shield.fill" : "shield.slash")
                    .foregroundColor(adBlockManager.isEnabled ? .green : .red)
                    .font(.title2)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("广告屏蔽")
                        .font(.headline)
                    Text(adBlockManager.isEnabled ? "已启用" : "已禁用")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Toggle("", isOn: Binding(
                    get: { adBlockManager.isEnabled },
                    set: { adBlockManager.setEnabled($0) }
                ))
            }
            
            // 测试站点选择
            VStack(alignment: .leading, spacing: 8) {
                Text("测试站点")
                    .font(.headline)
                
                Picker("测试站点", selection: $selectedTestSite) {
                    ForEach(AdBlockTestSite.allCases) { site in
                        Text(site.displayName).tag(site)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            
            // 统计信息
            HStack(spacing: 20) {
                AdBlockStatCard(
                    title: "已屏蔽",
                    value: "\(adBlockManager.statistics.totalRequestsBlocked)",
                    icon: "shield.checkered",
                    color: .green
                )

                AdBlockStatCard(
                    title: "节省流量",
                    value: adBlockManager.statistics.displayBytesBlocked,
                    icon: "arrow.down.circle",
                    color: .blue
                )

                AdBlockStatCard(
                    title: "活跃规则",
                    value: "\(adBlockManager.activeRuleLists.count)",
                    icon: "list.bullet",
                    color: .purple
                )
            }
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - WebView容器
    private var webViewContainer: some View {
        ZStack {
            if let webView = webView {
                WebViewRepresentable(webView: webView, isLoading: $isLoading, error: $loadError)
            } else {
                VStack(spacing: 16) {
                    Image(systemName: "globe")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)
                    
                    Text("正在初始化WebView...")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    ProgressView()
                }
            }
            
            if isLoading {
                VStack {
                    Spacer()
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("加载中...")
                            .font(.caption)
                    }
                    .padding()
                    .background(Color(.systemBackground))
                    .cornerRadius(8)
                    .shadow(radius: 2)
                    .padding()
                }
            }
        }
    }
    
    // MARK: - 结果面板
    private var resultsPanel: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("测试结果")
                .font(.headline)
            
            if let error = loadError {
                Label(error, systemImage: "exclamationmark.triangle")
                    .foregroundColor(.red)
                    .font(.caption)
            } else {
                VStack(alignment: .leading, spacing: 8) {
                    Label("页面加载完成", systemImage: "checkmark.circle")
                        .foregroundColor(.green)
                        .font(.caption)
                    
                    Text("建议访问 \(selectedTestSite.displayName) 查看详细的屏蔽效果报告")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Button("查看详细统计") {
                // 这里可以导航到详细的统计页面
            }
            .font(.caption)
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - 私有方法
    
    private func setupWebView() {
        let configuration = WKWebViewConfiguration()
        
        // 应用广告屏蔽规则
        adBlockManager.applyToWebViewConfiguration(configuration)
        
        let newWebView = WKWebView(frame: .zero, configuration: configuration)
        newWebView.navigationDelegate = WebViewCoordinator()
        
        self.webView = newWebView
    }
    
    private func runAdBlockTest() {
        guard let webView = webView else { return }
        
        showingResults = false
        loadError = nil
        
        let url = URL(string: selectedTestSite.url)!
        let request = URLRequest(url: url)
        
        webView.load(request)
        
        // 延迟显示结果面板
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            showingResults = true
        }
    }
}

// MARK: - 测试站点枚举
enum AdBlockTestSite: String, CaseIterable, Identifiable {
    case adblockTester = "adblock_tester"
    case testMyAdBlocker = "test_my_adblocker"
    case canYouBlockIt = "can_you_block_it"
    
    var id: String { rawValue }
    
    var displayName: String {
        switch self {
        case .adblockTester:
            return "AdBlock Tester"
        case .testMyAdBlocker:
            return "Test My AdBlocker"
        case .canYouBlockIt:
            return "Can You Block It"
        }
    }
    
    var url: String {
        switch self {
        case .adblockTester:
            return "https://adblock-tester.com"
        case .testMyAdBlocker:
            return "https://testmyadblocker.com"
        case .canYouBlockIt:
            return "https://canyoublockit.com"
        }
    }
    
    var description: String {
        switch self {
        case .adblockTester:
            return "全面的广告屏蔽效果测试"
        case .testMyAdBlocker:
            return "简单的广告屏蔽器测试"
        case .canYouBlockIt:
            return "挑战性的广告屏蔽测试"
        }
    }
}

// MARK: - 广告屏蔽统计卡片组件
struct AdBlockStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 4) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title3)
            
            Text(value)
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 8)
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

// MARK: - WebView包装器
struct WebViewRepresentable: UIViewRepresentable {
    let webView: WKWebView
    @Binding var isLoading: Bool
    @Binding var error: String?
    
    func makeUIView(context: Context) -> WKWebView {
        webView.navigationDelegate = context.coordinator
        return webView
    }
    
    func updateUIView(_ uiView: WKWebView, context: Context) {
        // 不需要更新
    }
    
    func makeCoordinator() -> WebViewCoordinator {
        WebViewCoordinator(isLoading: $isLoading, error: $error)
    }
}

// MARK: - WebView协调器
class WebViewCoordinator: NSObject, WKNavigationDelegate {
    @Binding var isLoading: Bool
    @Binding var error: String?
    
    init(isLoading: Binding<Bool> = .constant(false), error: Binding<String?> = .constant(nil)) {
        self._isLoading = isLoading
        self._error = error
    }
    
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        isLoading = true
        error = nil
    }
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        isLoading = false
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        isLoading = false
        self.error = error.localizedDescription
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        isLoading = false
        self.error = error.localizedDescription
    }
    
    // 记录被屏蔽的请求
    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        
        // 这里可以添加自定义的屏蔽逻辑
        // 但主要的屏蔽工作由WKContentRuleList完成
        
        decisionHandler(.allow)
    }
}

#Preview {
    NavigationView {
        AdBlockTestView()
    }
}
