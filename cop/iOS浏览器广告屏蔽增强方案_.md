

# **增强iOS浏览器广告屏蔽能力：从WKUserScript到EasyList与WKContentRuleList的演进方案**

## **执行摘要**

本报告旨在为iOS浏览器开发者提供一份战略性指导，以实现从基于WKUserScript的广告屏蔽方案向更强大、高效且易于维护的WKContentRuleList与EasyList规则集成方案的转变。当前WKUserScript的实现方式虽然能进行基本的广告移除，但其在页面加载后执行的特性导致了性能瓶颈和广告闪烁问题。相比之下，WKContentRuleList能够在WebKit引擎层面进行预加载、声明式的广告及内容屏蔽，从而显著提升用户体验、保护用户隐私并优化浏览器性能。本报告将深入探讨EasyList的结构、WKContentRuleList的技术细节、解析和集成大型过滤列表的实用步骤，并提供高级优化策略以应对iOS平台特有的限制。

## **1\. 引言：iOS浏览器广告屏蔽的演进**

### **当前状态：基于WKUserScript的广告屏蔽及其固有限制**

目前，轻量级iOS浏览器采用WKUserScript注入JavaScript来实现广告屏蔽功能。这种方法通常涉及在网页内容加载完成后执行脚本，进而操作DOM（文档对象模型）来移除或隐藏广告元素。例如，通过JavaScript代码 $(\\".ad\_slot\\" ).remove(); 来移除带有特定类名的广告位 1。

然而，这种基于JavaScript注入的广告屏蔽方案存在诸多固有限制：

* **性能开销与视觉闪烁：** JavaScript注入和DOM操作发生在页面资源下载之后，这意味着广告内容仍然会被下载，导致页面加载速度变慢，并且用户可能会短暂看到广告出现后再被移除，产生“闪烁”效应，严重影响用户体验 2。  
* **易于规避：** 广告加载方式日益复杂，许多广告通过JavaScript动态生成或采用反广告屏蔽技术。WKUserScript注入的脚本可能执行过晚，或容易被这些高级技术所规避，从而降低屏蔽的可靠性。  
* **维护复杂性：** 广告生态系统不断演变，手动编写和维护针对特定广告模式的JavaScript规则是一项艰巨且不可持续的任务。随着新广告形式的出现，自定义规则会迅速过时。  
* **屏蔽范围有限：** WKUserScript主要作用于DOM层面，无法在网络请求发起之前阻止广告资源的下载。这导致了带宽的浪费和设备电池的额外消耗，尤其在移动环境中，这是一种显著的资源浪费 3。  
* **Apple的平台建议：** Apple明确不鼓励在WKWebView中注入JavaScript以支持某些高价值功能，例如Apple Pay和应用绑定域（App-Bound Domains）。注入JavaScript会禁用这些安全和隐私增强功能 4。这表明Apple在架构上倾向于原生内容屏蔽机制。

### **强大广告屏蔽的必要性：用户体验、隐私与数据使用**

随着互联网广告的日益泛滥，用户对更洁净、更快速、更私密的网络体验的需求变得前所未有的迫切 3。侵入性广告不仅干扰浏览流程，还会消耗用户的移动数据流量，并加速设备电池的消耗 3。因此，一个强大有效的广告屏蔽方案能够显著提升用户满意度，缩短页面加载时间，并降低数据消耗，这对于移动浏览器用户而言至关重要 3。此外，除了广告本身，过滤列表还能有效阻止跟踪器、弹窗及其他烦人的元素，从而大幅提升用户隐私保护水平 3。

### **EasyList为何成为广告屏蔽的行业标准**

EasyList被誉为“广告屏蔽的支柱”，它是一个开放源代码、由社区运营并频繁更新的规则列表，被大多数主流广告屏蔽器（如uBlock Origin、AdBlock Plus、Brave等）所采用 3。它提供了识别常规内容与广告的“洞察力”或“智能”，通过匹配URL、脚本和页面元素等特定模式来识别和阻止广告 5。

EasyList项目的核心目标是通过消除所有广告，支持更清洁、更快速的网络浏览体验 5。它严格屏蔽广告，不设例外，这与一些允许非侵入性广告的“白名单”产品有所不同 5。

在广告屏蔽领域，存在着一场持续的“猫鼠游戏”。广告商不断尝试通过各种技术规避屏蔽，例如使用“轮转域名”（revolving domains）来逃避过滤器 8。然而，EasyList能够有效应对这些规避技术，其成功之处在于其社区驱动的特性和频繁的更新机制。EasyList的志愿者过滤列表作者会根据用户反馈和新的广告模式持续更新规则 5。这种动态适应能力是其保持有效性的关键。因此，依赖EasyList意味着将识别和屏蔽新广告技术的巨大且持续的工作量外包给一个专业的志愿者社区。这相较于开发者自行维护一套定制的JavaScript注入规则而言，是显著的优势，因为后者很快就会过时且效果不佳。

## **2\. EasyList深度解析：广告屏蔽标准**

### **EasyList的开源性质与社区驱动更新**

EasyList是广告屏蔽领域的基础性、开放源代码且由社区维护的项目，它定义了全球范围内的广告屏蔽规则 5。其规则列表由志愿者过滤列表作者持续更新，这些更新基于用户请求和新兴广告模式 5。这种机制确保了EasyList能够有效对抗新的广告格式和规避尝试 5。EasyList是公开可用的，任何人都可以访问或下载其过滤列表 5。

### **EasyList的核心组成部分**

EasyList的规则主要分为两大类：网络过滤规则和元素隐藏规则，并辅以例外规则。

* 网络过滤规则（屏蔽URL、域名、资源）  
  这些规则旨在阻止与广告和跟踪器相关的特定URL、域名和脚本的加载 6。例如，规则  
  /banner/\*/img^ 可以阻止包含 /banner/ 和 img 的URL，而 ||ads.example.com/foo.gif 规则则可以根据域名和资源类型阻止特定GIF文件的加载 9。EasyList的语法通常涉及使用通配符（  
  \*）、锚点（|）和分隔符（^）来定义网络请求的屏蔽模式 9。值得注意的是，EasyList会直接阻止那些利用“轮转域名”来逃避过滤器的“滥用性广告服务器” 8。  
* 元素隐藏规则（通过CSS选择器隐藏页面元素）  
  这些规则通过CSS选择器来隐藏网页上的特定HTML元素，即使广告内容已被下载，也能阻止其显示 9。其语法通常是  
  \#\# 后跟一个CSS选择器，例如 \#\#.advert 用于隐藏所有类名为“advert”的元素 9。更复杂的选择器可以根据ID（  
  \#id）、属性（\[attribute\]）或层级关系（div \>.link）来定位元素 9。  
* 例外规则（白名单）  
  EasyList允许为屏蔽规则设置例外，通常用于“误报”情况（即合法内容被错误屏蔽） 5。网络请求的例外规则使用  
  @@，而元素隐藏的例外规则使用 \#@\# 9。例如，  
  \#@\#\[href\*="/promo"\] 可以禁用针对特定元素的隐藏规则 10。

在广告屏蔽的生态系统中，存在着一种平衡：EasyList致力于严格屏蔽广告，不设例外 5。然而，由eyeo公司管理的“可接受广告标准”则允许展示符合特定标准（如非侵入性、清晰标注）的非干扰性广告 3。eyeo的白名单规则可以覆盖EasyList的屏蔽规则 5。这种对比揭示了广告屏蔽领域的一个核心矛盾：是追求绝对屏蔽，还是同时支持出版商通过“可接受”广告实现可持续的盈利模式。对于浏览器开发者而言，理解这种区别至关重要。如果目标是绝对屏蔽，EasyList足以胜任；如果希望支持“公平互联网”模式或提供用户选择，则需要额外集成“可接受广告”白名单。鉴于用户明确要求“增强”广告屏蔽能力，这暗示了对更严格屏蔽的偏好，与EasyList的核心使命相符。

### **相关列表概述（EasyPrivacy, Fanboy's Annoyances）**

EasyList通常与其他专业过滤列表结合使用，以提供更全面的屏蔽效果 3。

* **EasyPrivacy：** 作为EasyList的一个分支，它专门针对跟踪器、网络爬虫和跟踪脚本，以增强用户隐私 6。它会阻止分析工具、遥测、跟踪像素和CNAME跟踪器等 8。  
* **Fanboy's Annoyances：** 该列表旨在屏蔽页面内弹窗、社交媒体小部件、Cookie警告以及其他“烦人”的网页元素，从而减少页面混乱 6。这包括使用跟踪技术的“同意消息” 8。  
* **I Don't Care About Cookies：** 此列表用于阻止网站Cookie警告的弹出，通常通过隐藏这些通知来实现，而非拒绝权限 11。

这些列表虽然各自侧重不同，但它们是互补的。EasyList专注于广告，EasyPrivacy专注于跟踪器，而Fanboy's Annoyances则处理一般性的干扰元素。因此，为了实现用户所期望的“增强”广告屏蔽能力，仅仅屏蔽传统广告是不够的，还需要将屏蔽范围扩展到跟踪行为和侵入性UI元素。这意味着在解决方案设计时，必须考虑集成EasyPrivacy和Fanboy's Annoyances等列表。为此，解析和管理基础设施必须足够灵活，以处理多个大型过滤列表。

## **3\. 转向原生iOS内容屏蔽：WKContentRuleList**

### **3.1. WKContentRuleList 与 WKUserScript：技术对比**

在iOS平台上实现广告屏蔽，开发者面临两种主要技术选择：WKUserScript和WKContentRuleList。理解它们之间的差异对于选择最佳的增强方案至关重要。

| 特性/方面 | WKUserScript (当前方案) | WKContentRuleList (推荐方案) |
| :---- | :---- | :---- |
| **执行时机** | DOM加载后注入并执行 (atDocumentEnd) 或之前 (atDocumentStart)；evaluateJavaScript 可按需执行 13 | 在网络请求发起前，WebKit引擎层面进行处理 14 |
| **屏蔽机制** | 通过JavaScript操作DOM，移除或隐藏元素 1 | 使用JSON规则声明式地阻止网络请求、通过CSS隐藏元素、或修改请求 14 |
| **性能影响** | 可能导致广告短暂出现后消失的“闪烁”；资源先下载后处理，影响加载速度 2 | 高效阻止资源下载，页面加载更快，无闪烁 16 |
| **数据使用** | 高（所有资源均下载） | 低（阻止不必要资源的下载） 3 |
| **电池消耗** | 高 | 低 3 |
| **规避难度** | 中等，易受反广告屏蔽技术影响 | 高，在网络层阻止，更难被规避 |
| **维护成本** | 高（需手动维护JS规则） | 低（依赖EasyList等社区维护列表） |
| **Apple推荐** | 不鼓励用于某些高价值功能，可能禁用安全特性 4 | 官方推荐的声明式内容屏蔽API 15 |
| **高级能力** | 完整的JavaScript能力，可实现复杂逻辑 | 主要限于URL过滤和CSS隐藏；不支持高级JS脚本注入 (scriptlets) 或部分高级CSS伪选择器 19 |
| **隐私保护** | 较低（JS可访问DOM和用户数据） | 较高（内容屏蔽器无法访问用户历史或网站数据） 15 |

**WKContentRuleList的优势：**

* **原生性能：** WKContentRuleList规则由WebKit编译为高效的字节码，处理速度极快，对浏览器性能影响极小 16。这意味着广告屏蔽的开销几乎可以忽略不计。  
* **WebKit层面屏蔽：** 它在网络请求发起前就进行干预，阻止广告资源被下载、解析或渲染，从而节省了大量数据流量和电池能量 3。这从根本上解决了WKUserScript的性能和资源浪费问题。  
* **降低内存占用：** 通过阻止不必要的资源加载，WKContentRuleList自然地减少了与广告相关的内存消耗。  
* **增强安全与隐私：** 内容屏蔽器在设计上无法访问用户的浏览历史或网站数据，这使其成为更注重隐私的屏蔽机制 15。同时，它不会像WKUserScript那样禁用其他WebKit安全功能 4。  
* **Apple的官方推荐：** WKContentRuleList是Apple为Safari扩展和WKWebView提供的官方、声明式内容屏蔽API 15，这意味着它得到了Apple的持续支持和优化。

**WKContentRuleList的局限性：**

* **规则数量限制：** 每个WKContentRuleList对象可包含的规则数量存在实际限制。虽然一些资料提及50,000条规则，但另一些则指出Safari内容过滤的每类别规则上限可达150,000条，而系统级DNS过滤的上限约为530,000条 20。超出此限制可能导致应用崩溃 20。  
* **CSS选择器局限性：** WKContentRuleList支持标准CSS选择器 22。然而，它通常不支持桌面广告屏蔽器（如uBlock Origin）中使用的某些高级伪选择器（如  
  :has-text()或脚本注入） 19。这意味着EasyList中一些复杂的元素隐藏规则可能无法直接转换。  
* **请求拦截限制（iOS 11之前）：** 在iOS 11之前，WKWebView没有直接拦截网络请求的API，因此WKContentRuleList功能不可用 14。

### **3.2. WKContentRuleList规则的结构**

WKContentRuleList规则以JSON数组的形式定义，其中每个字典代表一条独立的规则 14。每条规则主要包含两个部分：一个

trigger字典和一个action字典 14。

**JSON结构示例：**

JSON

\[  
  {  
    "trigger": {  
      "url-filter": ".\*",  
      "resource-type": \["image", "style-sheet"\],  
      "unless-domain": \["your-content-server.com"\]  
    },  
    "action": {  
      "type": "block"  
    }  
  },  
  {  
    "trigger": {  
      "url-filter": "googleads.g.doubleclick.net\*",  
      "if-domain": \["example.com"\]  
    },  
    "action": {  
      "type": "block"  
    }  
  },  
  {  
    "trigger": {  
      "url-filter": ".\*",  
      "if-domain": \["example.com"\]  
    },  
    "action": {  
      "type": "css-display-none",  
      "selector": ".ad-banner"  
    }  
  },  
  {  
    "trigger": {  
      "url-filter": ".\*.jpeg"  
    },  
    "action": {  
      "type": "ignore-previous-rules"  
    }  
  }  
\]

**trigger字段的详细解释：**

* url-filter：一个字符串，表示用于匹配资源URL的正则表达式 15。  
  .\* 表示匹配所有URL 15。  
* url-filter-is-case-sensitive：可选布尔值，默认为false 15。  
* resource-type：一个字符串数组，指定资源类型（例如，image、script、style-sheet、document、font、media、popup、xmlhttprequest、websocket、fetch、raw、other） 15。如果省略，则匹配所有类型。  
* if-domain：一个字符串数组（域名），表示规则应该在哪些域名上应用。可以使用\*来匹配子域名（例如，\*.example.com） 15。与  
  unless-domain互斥。  
* unless-domain：一个字符串数组（域名），表示规则不应该在哪些域名上应用 15。与  
  if-domain互斥。  
* load-type：一个字符串数组，包含两个互斥值：first-party（与主页面具有相同协议、域名和端口的资源）或third-party（来自不同域名的资源） 15。  
* if-top-url / unless-top-url：匹配主文档的完整URL 15。  
* if-frame-url / unless-frame-url：匹配框架文档的URL 15。  
* load-context：指定加载上下文的字符串数组 15。

**action类型的详细解释：**

* type：必需字段，定义操作类型 15。  
* block：停止加载资源。忽略缓存 14。用于网络请求屏蔽。  
* css-display-none：根据CSS选择器隐藏页面元素。将元素的display属性设置为none 14。需要一个  
  selector字段。  
* ignore-previous-rules：忽略之前为匹配资源触发的任何操作 14。对于白名单功能至关重要。  
* block-cookies：在发送到服务器之前从请求头中剥离Cookie 15。  
* make-https：将HTTP URL更改为HTTPS 15。  
* selector：与css-display-none一起使用，指定要隐藏元素的CSS选择器 15。支持标签、类、ID和属性选择器等标准CSS选择器 9。

WKContentRuleList提供了细致的trigger字段，例如resource-type、if-domain和load-type 15。这种细粒度控制比简单的JavaScript注入更精确，后者可能屏蔽过于宽泛或执行过晚。这种精确性使得规则能够更准确地应用，减少了误报（即错误地屏蔽了合法内容），并确保规则仅在必要时才生效。由于这些规则被编译成高效的字节码 16，这种细粒度控制并不会带来性能损失，这与复杂的JavaScript逻辑形成了鲜明对比。这种精确的规则应用不仅是性能优化的最佳实践，也是最大程度减少“误报”和确保网站兼容性的关键。

## **4\. 实现EasyList与WKContentRuleList的集成**

### **4.1. 获取和管理EasyList数据**

要将EasyList规则集成到iOS浏览器中，首先需要获取并有效管理这些庞大的过滤列表数据。

* **下载EasyList及补充列表：** EasyList、EasyPrivacy和Fanboy's Annoyances等列表通常以.txt文件的形式公开提供，可以从easylist.to网站或其GitHub仓库直接下载 5。例如，可以使用  
  https://easylist-downloads.adblockplus.org/easyprivacy\_nointernational.txt等URL进行直接下载 23。  
* **高效更新过滤列表的策略：** 过滤列表由专门的志愿者社区“持续更新” 3。这要求浏览器具备可靠的更新机制。一种常见的策略是实现定期下载最新版本过滤列表的机制（例如，每天或每周一次） 3。虽然研究资料没有明确提及EasyList的增量更新格式，但Adblock Plus的工具（  
  python-abp）可以生成差异文件 24。这暗示了下载增量更新而非完整列表可能更高效，特别是对于大型列表。然而，WKContentRuleListStore的  
  compileContentRuleList方法会用新内容覆盖具有相同标识符的旧规则列表 18。这表明，对于WKContentRuleList而言，每次更新可能都需要进行完整的重新编译，而非增量应用差异。  
* **大型数据集的本地存储考量：** 编译后的WKContentRuleList对象会由WebKit持久化存储 16。这意味着原始的过滤列表文本无需持续保存在内存中。然而，原始文本文件可能非常大（例如，EasyList可能有数百KB）。在转换过程中，应考虑采用高效的存储方式（如压缩文件）和解析策略，以最大限度地减少内存占用。

### **4.2. 将EasyList规则解析为WKContentRuleList JSON**

EasyList的规则语法与WKContentRuleList所需的JSON格式并不直接兼容，因此需要一个转换层来将EasyList规则解析并映射为WKContentRuleList所需的JSON结构。

**挑战：EasyList语法与WKContentRuleList JSON不直接兼容。** EasyList使用其独特的过滤语法（例如，||domain^、\#\#.selector、@@） 9。而WKContentRuleList则要求特定的JSON格式，包含

trigger和action字典 14。因此，必须构建一个转换层。

**EasyList规则类型与WKContentRuleList JSON映射示例：**

| EasyList规则类型 | EasyList示例 | WKContentRuleList JSON等效示例 |
| :---- | :---- | :---- |
| **网络屏蔽** | \` |  |
| **网络屏蔽 (带类型)** | \` |  |
| **元素隐藏** | \#\#.ad-banner | json { "trigger": { "url-filter": ".\*" }, "action": { "type": "css-display-none", "selector": ".ad-banner" } } |
| **元素隐藏 (带域名)** | example.com,example.net\#\#.advert | json { "trigger": { "url-filter": ".\*", "if-domain": \["example.com", "example.net"\] }, "action": { "type": "css-display-none", "selector": ".advert" } } |
| **网络例外** | \`@@ |  |
| **元素隐藏例外** | \#@\#.promo-div | json { "trigger": { "url-filter": ".\*" }, "action": { "type": "ignore-previous-rules", "selector": ".promo-div" } } |

**开源解析库及其对Swift/iOS的适用性讨论：**

手动解析整个EasyList是一项巨大的工程。利用现有的开源解析器是高度推荐的方案。

* nwoolls/EasyListParser (Swift)：  
  这是一个专门为Safari内容屏蔽器从EasyList过滤器创建列表的iOS框架 23。它主要用Swift编写（占98.4%） 23。该库能够显式解析EasyList内容并将其序列化为  
  ELBlockerEntry对象，这些对象随后可以转换为JSON格式 23。它支持  
  maxEntries（Safari允许的最大条目数，默认为50,000）和trustedDomains 23。  
  该项目与Safari内容屏蔽器API高度契合 23。鉴于WKContentRuleList使用的语法与Safari内容屏蔽扩展“相同” 18，这使得  
  nwoolls/EasyListParser成为用户任务的极佳选择。其直接输出格式正是WKContentRuleListStore所期望的。这个库可以显著减少开发工作量，并确保与Apple原生屏蔽机制的兼容性，因此是强烈推荐的方案。  
* LukeChannings/EasyListSafari (Swift/JavaScript)：  
  该项目旨在将EasyList屏蔽规则移植到iOS 9移动Safari内容屏蔽器 29。它大约翻译了55%的规则 29。该项目指出了  
  if-domain中非拉丁字符的问题以及严格的JSON格式要求 29。  
  鉴于WKContentRuleList是在iOS 11中引入的 14，而此项目针对的是iOS 9 29，其“大约55%的规则已翻译”的现状以及目标iOS版本表明，它可能对于完整的EasyList规范或现代iOS API而言已不完整或过时。尽管具有历史意义，但与  
  nwoolls/EasyListParser相比，该项目可能不是现代iOS 11+应用的最佳选择，因为它可能存在不完整性和维护状态问题。  
* SergeyZhukovsky/abp-filter-parser-cpp (C++)：  
  这是一个用于Adblock Plus过滤规则解析的C++库，它使用布隆过滤器和Rabin-Karp算法以实现快速性能 30。C++代码通常可以集成到iOS项目中，因此该库可以被桥接到Objective-C/Swift中使用 30。  
  该库强调其C++实现和高级算法带来的“超快”性能 30。然而，将C++库集成到以Swift为主的iOS项目中会引入桥接复杂性（例如，Objective-C++包装器）和额外的构建步骤。开发者需要权衡潜在的性能提升与增加的开发和维护开销。虽然该库可能提供顶级的解析性能，但对于许多iOS开发者而言，它可能会引入不必要的复杂性，特别是当像  
  nwoolls/EasyListParser这样的Swift原生解决方案已经提供了“足够好”的性能时。  
* 通用Swift解析库（例如，swift-parsing）：  
  像pointfreeco/swift-parsing这样的库提供了强大、可组合且高性能的纯Swift解析能力 31。然而，EasyList具有复杂的、不断演变的语法，包含许多细微之处（网络规则、美化规则、例外、选项） 9。使用通用解析库从头开始构建一个自定义的EasyList解析器将涉及巨大的开发工作量，以实现完整的EasyList语法及其所有细节。除非现有解决方案无法满足非常特定的需求，否则为EasyList从零开始构建自定义解析器很可能是一种过度工程，因为过滤列表的复杂性和持续演变。

### **4.3. 在WKWebView中编译和应用规则**

将EasyList规则成功解析为WKContentRuleList JSON格式后，下一步是在WKWebView中编译并应用这些规则。

* 使用WKContentRuleListStore.default().compileContentRuleList(...)：  
  这是核心API，用于将JSON规则字符串转换为编译后的WKContentRuleList对象 25。该方法需要一个  
  identifier（列表的唯一标识符字符串）和encodedContentRuleList（JSON字符串）作为参数 25。编译过程是异步的，并返回一个WKContentRuleList对象或一个错误 25。  
  以下是编译和应用规则的Swift代码示例：  
  Swift  
  import WebKit

  func applyContentBlockingRules(webView: WKWebView, rulesJson: String, identifier: String) {  
      WKContentRuleListStore.default().compileContentRuleList(  
          forIdentifier: identifier,  
          encodedContentRuleList: rulesJson  
      ) { (contentRuleList, error) in  
          if let error \= error {  
              print("Error compiling content rule list: \\(error.localizedDescription)")  
              return  
          }  
          if let ruleList \= contentRuleList {  
              webView.configuration.userContentController.add(ruleList)  
              print("Content rule list '\\(identifier)' applied successfully.")  
          }  
      }  
  }

  14  
* 将编译后的WKContentRuleList对象添加到WKUserContentController：  
  一旦规则列表被编译，WKContentRuleList对象就需要添加到WKWebViewConfiguration的userContentController中 14。具体操作是调用  
  webView.configuration.userContentController.add(ruleList)方法 14。  
* 管理多个规则列表：  
  为了实现全面的广告屏蔽，建议使用多个过滤列表（例如，EasyList、EasyPrivacy、Fanboy's Annoyances） 3。每个列表都应使用唯一的标识符进行编译。WKUserContentController可以添加多个WKContentRuleList对象 34。  
  WKContentRuleList存在每列表的规则数量限制（5万至15万条） 20。一个单独的、庞大的EasyList（可能包含数十万条规则）很可能会超出此限制。然而，WKContentRuleListStore允许管理多个独立的WKContentRuleList对象 26。因此，将大型过滤列表拆分为逻辑上更小、分类更明确的WKContentRuleList对象（例如，“EasyList广告”、“EasyPrivacy跟踪器”、“Fanboy烦扰”）是一种有效的架构设计模式，可以帮助规避平台限制。这种模块化方法不仅有助于避免应用崩溃，还能通过允许WebKit处理更小、更集中的规则集来提高感知性能。此外，这种方法也为未来提供用户可配置的屏蔽类别提供了途径，增强了用户对屏蔽内容的控制力。  
* 处理规则列表更新和移除（removeAllContentRuleLists()）：  
  为了更新规则列表，首先需要移除现有的列表（可以使用removeAllContentRuleLists()移除所有列表，或使用remove(forIdentifier:)移除特定列表），然后再编译并添加新版本 14。这确保了始终应用最新的规则。

## **5\. 高级考量与优化**

### **5.1. 性能与规则限制**

* 理解WKContentRuleList的规则限制：  
  iOS上的Safari内容屏蔽器对每个列表的规则数量有实际限制。虽然一些资料提到50,000条规则，但其他资料则表明每类别规则上限可达150,000条 20。超出此限制可能导致应用程序崩溃 20。对于包含数十万条规则的综合性过滤列表（如EasyList），这一限制是关键的约束。  
* **优化大型过滤列表的策略：**  
  * 将大型列表拆分为多个更小、分类的列表：  
    如前所述，将一个巨大的组合过滤列表拆分为逻辑上更小、更具体的WKContentRuleList对象（例如，“EasyList广告”、“EasyPrivacy跟踪器”、“Fanboy烦扰”）有助于保持在每列表的规则限制内 35。这种做法也与EasyList及其补充列表的模块化性质相符 11。  
    WKContentRuleList的规则数量限制 20 是一个直接的平台约束。一个单独的、合并的EasyList很可能会超出这个限制。然而，WKContentRuleListStore允许管理多个独立的WKContentRuleList对象 26。因此，将大型过滤列表拆分为更小、分类的列表（例如EasyList、EasyPrivacy、Fanboy's Annoyances）是一种设计模式，旨在与平台的限制协同工作，而非对抗。这种多列表策略不仅是为了避免崩溃，还能通过允许WebKit处理更小、更集中的规则集来提高感知性能。它也为未来用户可配置的屏蔽类别提供了途径。  
  * 优先处理核心规则：  
    可以考虑向用户提供选择启用哪些列表的选项，让他们在屏蔽的激进程度与潜在的网站兼容性问题之间取得平衡 3。对于一个“轻量级”浏览器来说，核心的EasyList规则可能已足够，而更重的列表可以作为高级用户的可选功能。  
  * 利用if-domain/unless-domain进行定向屏蔽：  
    这些trigger字段 15 允许规则仅应用于特定域名或从某些域名中排除。这可以缩小广泛规则的作用范围，通过限制不必要的检查来潜在地提高性能。  
    宽泛的url-filter正则表达式（例如.\*）与css-display-none结合使用虽然强大，但也存在风险，可能错误地隐藏合法内容或影响许多网站的性能。使用if-domain或unless-domain 15 缩小这些规则的作用范围，是一种优化形式，即使规则在技术上是“活跃的”，也能减少需要对其进行评估的网站数量。这种精确的规则应用不仅有助于提高性能，还能通过防止意外的内容屏蔽来维持流畅的用户体验。  
  * 监控性能影响：  
    广告屏蔽通常可以通过阻止大型广告脚本来提高浏览速度 3。然而，过多的列表也可能导致性能问题或破坏网站功能 3。应定期对浏览器的性能（CPU、内存、加载时间）进行分析，并使用不同的过滤列表配置进行测试，以确保流畅的用户体验。

### **5.2. 处理复杂广告屏蔽场景**

* WKContentRuleList对动态或脚本规避广告的局限性：  
  尽管WKContentRuleList在网络请求和CSS层面上的屏蔽功能强大，但与功能齐全的桌面广告屏蔽器相比，它存在固有的局限性。它不支持任意JavaScript注入（scriptlets）或uBlock Origin和AdGuard用于复杂美化过滤或反广告屏蔽规避的高度高级CSS伪选择器（如:has-text()、:matches-attr()、:upward()或:matches-css()） 19。这意味着一些动态注入自身或使用非常细微、非标准DOM操作的广告可能仍然会绕过WKContentRuleList。  
* 潜在的混合方法需求：  
  对于WKContentRuleList难以处理的特别顽固的广告或反广告屏蔽墙，可以考虑作为备用方案，采用有限且有针对性的WKUserScript注入。  
  用户当前使用WKUserScript，并希望“增强”屏蔽能力。WKContentRuleList在大多数广告屏蔽场景中都表现出卓越的性能 2。然而，WKContentRuleList对于高级、动态广告场景存在已知局限性（例如，缺乏scriptlets、高级CSS选择器） 19。这意味着纯粹采用WKContentRuleList方案可能无法屏蔽所有复杂的广告，从而无法完全实现“增强”屏蔽能力的目标。因此，一个务实的解决方案可能是在WKContentRuleList无法有效处理的特定、高度复杂的场景中，保留WKUserScript作为补充手段。这是一种外科手术式的应用，而非普遍采用。这种混合方法承认了两种技术的优缺点，它优先利用WKContentRuleList在绝大多数广告屏蔽场景中的性能优势，同时为最具挑战性的情况提供有针对性的回退方案，从而在不牺牲整体性能的前提下，确保最大的广告屏蔽效果。然而，由于WKUserScript可能禁用其他WebKit功能 4，必须谨慎评估这种权衡。

## **6\. 结论与建议**

### **结论回顾**

将iOS浏览器的广告屏蔽方案从基于WKUserScript的JavaScript注入转向集成EasyList规则的WKContentRuleList，代表着一次显著的能力升级。这种转变带来了多重优势：通过在网络请求层面进行屏蔽，它提供了卓越的性能，显著降低了数据消耗，增强了用户隐私，并提供了更流畅、无缝的浏览体验。此外，利用EasyList等由社区维护的过滤列表，极大地减轻了开发者在规则维护上的巨大负担，并确保了广告屏蔽能力能够持续应对不断演变的广告技术。

### **对iOS开发者的关键建议**

1. **优先采用WKContentRuleList：** 将WKContentRuleList作为主要的广告屏蔽机制。其原生性能、效率以及在WebKit层面的屏蔽能力，使其成为当前iOS平台上实现高效广告屏蔽的最佳选择。  
2. **集成EasyList及相关列表：** 导入并定期更新EasyList、EasyPrivacy和Fanboy's Annoyances等核心过滤列表。这些列表共同提供了全面的广告、跟踪器和干扰元素屏蔽能力，能够大幅提升用户体验。  
3. **利用开源解析库：** 强烈推荐使用像nwoolls/EasyListParser这样的Swift原生开源库，将EasyList规则解析并转换为WKContentRuleList所需的JSON格式。这能够显著减少开发工作量，并确保与Apple原生API的兼容性。  
4. **实施多列表策略：** 鉴于WKContentRuleList的规则数量限制，建议将庞大的过滤列表拆分为多个逻辑上更小、分类更明确的WKContentRuleList对象。这不仅有助于规避平台限制，还能提高性能，并为用户提供更精细的屏蔽选项。  
5. **谨慎采用混合方法：** 对于WKContentRuleList无法有效处理的少数极端复杂或动态的广告场景，可以考虑有选择性地、有限地使用WKUserScript作为补充。然而，必须充分评估这种混合方法可能带来的性能影响和对其他WebKit功能（如App-Bound Domains）的潜在禁用。  
6. **持续性能监控：** 定期监控浏览器在应用广告屏蔽规则后的性能表现（包括页面加载时间、CPU和内存使用情况），以确保屏蔽效果与用户体验之间的平衡。  
7. **提供用户控制：** 考虑在浏览器中提供用户界面，允许用户启用或禁用不同的过滤列表类别，从而赋予用户更多控制权，以适应其个人偏好和网站兼容性需求。

#### **引用的著作**

1. how to remove ads in webview? | Apple Developer Forums, 访问时间为 六月 17, 2025， [https://developer.apple.com/forums/thread/46661](https://developer.apple.com/forums/thread/46661)  
2. Thoughts on Content Blockers \- GoSquared Blog, 访问时间为 六月 17, 2025， [https://www.gosquared.com/blog/thoughts-on-content-blockers](https://www.gosquared.com/blog/thoughts-on-content-blockers)  
3. Ad-Blocking Filter Lists: What They Are and How They Work \- Poper Blocker, 访问时间为 六月 17, 2025， [https://poperblocker.com/filter-lists/](https://poperblocker.com/filter-lists/)  
4. Explore WKWebView additions \- WWDC21 \- Videos \- Apple Developer, 访问时间为 六月 17, 2025， [https://developer.apple.com/videos/play/wwdc2021/10032/](https://developer.apple.com/videos/play/wwdc2021/10032/)  
5. Understanding EasyList: The backbone of ad blocking \- eyeo, 访问时间为 六月 17, 2025， [https://resources.eyeo.com/understanding-easylist-the-backbone-of-ad-blocking](https://resources.eyeo.com/understanding-easylist-the-backbone-of-ad-blocking)  
6. The EasyList, AdBlockers and Affiliate Marketing \- Multiplicit ltd, 访问时间为 六月 17, 2025， [https://www.multiplicit.co.uk/adblockers-the-easylist-and-affiliate-marketing/](https://www.multiplicit.co.uk/adblockers-the-easylist-and-affiliate-marketing/)  
7. ADmosphere \- Free Ad Blocker with EasyListy \- App Store, 访问时间为 六月 17, 2025， [https://apps.apple.com/us/app/admosphere-free-ad-blocker-with-easylisty/id1060126069](https://apps.apple.com/us/app/admosphere-free-ad-blocker-with-easylisty/id1060126069)  
8. easylist/easylist: EasyList filter subscription (EasyList, EasyPrivacy, EasyList Cookie, Fanboy's Social/Annoyances/Notifications Blocking List) \- GitHub, 访问时间为 六月 17, 2025， [https://github.com/easylist/easylist](https://github.com/easylist/easylist)  
9. Adblock Plus filters explained, 访问时间为 六月 17, 2025， [https://adblockplus.org/filter-cheatsheet](https://adblockplus.org/filter-cheatsheet)  
10. filter \- Adblock Plus \- whitelisting rule \- Stack Overflow, 访问时间为 六月 17, 2025， [https://stackoverflow.com/questions/35043424/adblock-plus-whitelisting-rule](https://stackoverflow.com/questions/35043424/adblock-plus-whitelisting-rule)  
11. Introduction to Filter Lists \- AdBlock, 访问时间为 六月 17, 2025， [https://helpcenter.getadblock.com/hc/en-us/articles/9738523403027-Introduction-to-Filter-Lists](https://helpcenter.getadblock.com/hc/en-us/articles/9738523403027-Introduction-to-Filter-Lists)  
12. EasyList \- Overview, 访问时间为 六月 17, 2025， [https://easylist.to/](https://easylist.to/)  
13. ios \- Javascript in WKWebView \- evaluateJavaScript vs addUserScript \- Stack Overflow, 访问时间为 六月 17, 2025， [https://stackoverflow.com/questions/41488423/javascript-in-wkwebview-evaluatejavascript-vs-adduserscript](https://stackoverflow.com/questions/41488423/javascript-in-wkwebview-evaluatejavascript-vs-adduserscript)  
14. How to block external resources to load on a WKWebView? \- Stack Overflow, 访问时间为 六月 17, 2025， [https://stackoverflow.com/questions/32119975/how-to-block-external-resources-to-load-on-a-wkwebview](https://stackoverflow.com/questions/32119975/how-to-block-external-resources-to-load-on-a-wkwebview)  
15. Creating a content blocker | Apple Developer Documentation, 访问时间为 六月 17, 2025， [https://developer.apple.com/documentation/safariservices/creating-a-content-blocker](https://developer.apple.com/documentation/safariservices/creating-a-content-blocker)  
16. ShingoFukuyama/AdsBlock\_WKWebView\_for\_iOS11: This is a sample app. iOS 11 introduces Safari like Content-Blocking Rules. \- GitHub, 访问时间为 六月 17, 2025， [https://github.com/ShingoFukuyama/AdsBlock\_WKWebView\_for\_iOS11](https://github.com/ShingoFukuyama/AdsBlock_WKWebView_for_iOS11)  
17. Comparing performance impact of ad blocking on different browsers \- Reddit, 访问时间为 六月 17, 2025， [https://www.reddit.com/r/browsers/comments/1ka7lfm/comparing\_performance\_impact\_of\_ad\_blocking\_on/](https://www.reddit.com/r/browsers/comments/1ka7lfm/comparing_performance_impact_of_ad_blocking_on/)  
18. WKContentRuleList \- Documentation \- Apple Developer, 访问时间为 六月 17, 2025， [https://developer.apple.com/documentation/webkit/wkcontentrulelist](https://developer.apple.com/documentation/webkit/wkcontentrulelist)  
19. Static filter syntax · gorhill/uBlock Wiki · GitHub, 访问时间为 六月 17, 2025， [https://github.com/gorhill/ublock/wiki/static-filter-syntax](https://github.com/gorhill/ublock/wiki/static-filter-syntax)  
20. Rule limits for adguard dns filters on ios? \- Reddit, 访问时间为 六月 17, 2025， [https://www.reddit.com/r/Adguard/comments/1i5yvpg/rule\_limits\_for\_adguard\_dns\_filters\_on\_ios/](https://www.reddit.com/r/Adguard/comments/1i5yvpg/rule_limits_for_adguard_dns_filters_on_ios/)  
21. Respect WKContentRuleList limit · Issue \#679 · AdguardTeam/AdGuardForSafari \- GitHub, 访问时间为 六月 17, 2025， [https://github.com/AdguardTeam/AdGuardForSafari/issues/679](https://github.com/AdguardTeam/AdGuardForSafari/issues/679)  
22. Using CSS selectors to hide elements | 1Blocker Help Center, 访问时间为 六月 17, 2025， [https://support.1blocker.com/en/articles/9312061-using-css-selectors-to-hide-elements](https://support.1blocker.com/en/articles/9312061-using-css-selectors-to-hide-elements)  
23. nwoolls/EasyListParser: Framework for creating Safari Content Blocker lists from EasyList filters \- GitHub, 访问时间为 六月 17, 2025， [https://github.com/nwoolls/EasyListParser](https://github.com/nwoolls/EasyListParser)  
24. python-abp \- PyPI, 访问时间为 六月 17, 2025， [https://pypi.org/project/python-abp/](https://pypi.org/project/python-abp/)  
25. compileContentRuleList(forIdentifier:encodedContentRuleList:completionHandler:) | Apple Developer Documentation, 访问时间为 六月 17, 2025， [https://developer.apple.com/documentation/webkit/wkcontentruleliststore/compilecontentrulelist(foridentifier:encodedcontentrulelist:completionhandler:)](https://developer.apple.com/documentation/webkit/wkcontentruleliststore/compilecontentrulelist\(foridentifier:encodedcontentrulelist:completionhandler:\))  
26. WKContentRuleListStore | Apple Developer Documentation, 访问时间为 六月 17, 2025， [https://developer.apple.com/documentation/webkit/wkcontentruleliststore?changes=\_8\_9](https://developer.apple.com/documentation/webkit/wkcontentruleliststore?changes=_8_9)  
27. WKContentRuleListStore | Apple Developer Documentation, 访问时间为 六月 17, 2025， [https://developer.apple.com/documentation/webkit/wkcontentruleliststore](https://developer.apple.com/documentation/webkit/wkcontentruleliststore)  
28. WKContentRuleList | Apple Developer Documentation, 访问时间为 六月 17, 2025， [https://developer.apple.com/documentation/webkit/wkcontentrulelist?language=objc](https://developer.apple.com/documentation/webkit/wkcontentrulelist?language=objc)  
29. LukeChannings/EasyListSafari: Port of EasyList rules to ... \- GitHub, 访问时间为 六月 17, 2025， [https://github.com/LukeChannings/EasyListSafari](https://github.com/LukeChannings/EasyListSafari)  
30. SergeyZhukovsky/abp-filter-parser-cpp: C++ Adblock Plus ... \- GitHub, 访问时间为 六月 17, 2025， [https://github.com/SergeyZhukovsky/abp-filter-parser-cpp](https://github.com/SergeyZhukovsky/abp-filter-parser-cpp)  
31. pointfreeco/swift-parsing: A library for turning nebulous data into well-structured data, with a focus on composition, performance, generality, and ergonomics. \- GitHub, 访问时间为 六月 17, 2025， [https://github.com/pointfreeco/swift-parsing](https://github.com/pointfreeco/swift-parsing)  
32. Intercepting requests from WebView \- DEV Community, 访问时间为 六月 17, 2025， [https://dev.to/quave/intercepting-requests-from-webview-1nin](https://dev.to/quave/intercepting-requests-from-webview-1nin)  
33. dequin-cl/WKContentRuleExample: Usage of WKContentRule to block content on WKWebView \- GitHub, 访问时间为 六月 17, 2025， [https://github.com/dequin-cl/WKContentRuleExample](https://github.com/dequin-cl/WKContentRuleExample)  
34. WKUserContentController | Apple Developer Documentation, 访问时间为 六月 17, 2025， [https://developer.apple.com/documentation/webkit/wkusercontentcontroller](https://developer.apple.com/documentation/webkit/wkusercontentcontroller)  
35. Efficient AdGuard Home \+ browser filter list setup: What's the best combo? \- Reddit, 访问时间为 六月 17, 2025， [https://www.reddit.com/r/Adguard/comments/1jwoli4/efficient\_adguard\_home\_browser\_filter\_list\_setup/](https://www.reddit.com/r/Adguard/comments/1jwoli4/efficient_adguard_home_browser_filter_list_setup/)